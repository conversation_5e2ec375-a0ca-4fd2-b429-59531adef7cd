using Entities.DTOs;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    /// <summary>
    /// Cache Owner Data Access Layer Interface - SOLID prensiplerine uygun
    /// Single Responsibility: Sadece cache data access operasyonları
    /// Interface Segregation: Sadece gerekli cache DAL metodları
    /// Dependency Inversion: Data access abstraction tanımı
    /// </summary>
    public interface ICacheOwnerDal
    {
        #region Company Cache Statistics

        /// <summary>
        /// Company'ye ait cache istatistiklerini Redis'ten hesaplar
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <returns>Cache istatistikleri</returns>
        Task<CacheStatisticsDto> GetCompanyCacheStatisticsAsync(int companyId);

        /// <summary>
        /// Tüm şirketlerin cache istatistiklerini getirir
        /// </summary>
        /// <returns>Çoklu şirket cache istatistikleri</returns>
        Task<MultiCompanyCacheStatisticsDto> GetAllCompaniesStatisticsAsync();

        #endregion

        #region Cache Health & Monitoring

        /// <summary>
        /// Redis cache sağlık durumunu kontrol eder
        /// </summary>
        /// <returns>Cache sağlık bilgileri</returns>
        Task<CacheHealthDto> GetCacheHealthAsync();

        /// <summary>
        /// Redis server bilgilerini getirir
        /// </summary>
        /// <returns>Server bilgileri</returns>
        Task<CacheServerInfoDto> GetRedisServerInfoAsync();

        /// <summary>
        /// Real-time cache metrics hesaplar
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <returns>Real-time metrics</returns>
        Task<CacheRealtimeMetricsDto> GetRealtimeCacheMetricsAsync(int companyId);

        #endregion

        #region Cache Key Operations

        /// <summary>
        /// Pattern'e göre cache key'lerini getirir (pagination ile)
        /// </summary>
        /// <param name="pattern">Arama pattern'i</param>
        /// <param name="page">Sayfa numarası</param>
        /// <param name="size">Sayfa boyutu</param>
        /// <returns>Cache key'leri</returns>
        Task<CacheKeysResponseDto> GetKeysByPatternAsync(string pattern, int page, int size);

        /// <summary>
        /// En çok kullanılan cache key'leri getirir
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="count">Getirilecek key sayısı</param>
        /// <returns>Top cache key'leri</returns>
        Task<CacheTopKeyDto[]> GetTopCacheKeysAsync(int companyId, int count);

        /// <summary>
        /// Cache key'inin değerini getirir
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Key değeri</returns>
        Task<CacheKeyValueDto> GetCacheKeyValueAsync(string key);

        #endregion

        #region Cache Clear Operations

        /// <summary>
        /// Company'nin tüm cache'ini temizler
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <returns>Temizleme sonucu</returns>
        Task<CacheClearResultDto> ClearCompanyCacheAsync(int companyId);

        /// <summary>
        /// Pattern'e göre cache'leri temizler
        /// </summary>
        /// <param name="pattern">Temizlenecek pattern</param>
        /// <returns>Temizleme sonucu</returns>
        Task<CacheClearResultDto> ClearCacheByPatternAsync(string pattern);

        /// <summary>
        /// Belirli cache key'ini siler
        /// </summary>
        /// <param name="key">Silinecek cache key</param>
        /// <returns>Silme sonucu</returns>
        Task<CacheKeyDeleteResultDto> DeleteCacheKeyAsync(string key);

        /// <summary>
        /// Çoklu şirket cache temizleme
        /// </summary>
        /// <param name="companyIds">Şirket ID'leri</param>
        /// <returns>Bulk temizleme sonucu</returns>
        Task<BulkCacheOperationResultDto> BulkClearCompaniesCache(int[] companyIds);

        #endregion

        #region Cache Warmup Operations

        /// <summary>
        /// Cache warmup işlemi gerçekleştirir
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="request">Warmup parametreleri</param>
        /// <returns>Warmup sonucu</returns>
        Task<CacheWarmupResultDto> PerformCacheWarmupAsync(int companyId, CacheWarmupRequestDto request);

        #endregion

        #region Company Cache Details

        /// <summary>
        /// Company cache detaylarını getirir
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <returns>Cache detayları</returns>
        Task<CacheDetailsDto> GetCompanyCacheDetailsAsync(int companyId);

        #endregion

        #region Security & Validation

        /// <summary>
        /// Cache key'inin company'ye ait olup olmadığını kontrol eder
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <param name="companyId">Company ID</param>
        /// <returns>True: ait, False: ait değil</returns>
        bool ValidateCacheKeyOwnership(string key, int companyId);

        /// <summary>
        /// Pattern'in company'ye ait olup olmadığını kontrol eder
        /// </summary>
        /// <param name="pattern">Cache pattern</param>
        /// <param name="companyId">Company ID</param>
        /// <returns>Güvenli pattern</returns>
        string ValidateAndSecurePattern(string pattern, int companyId);

        #endregion
    }
}
