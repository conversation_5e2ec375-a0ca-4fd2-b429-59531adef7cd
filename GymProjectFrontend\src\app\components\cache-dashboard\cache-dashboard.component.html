<div class="cache-dashboard-component">
  <!-- Header -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div>
      <h2 class="mb-1">
        <i class="fas fa-tachometer-alt me-2"></i>
        Cache Dashboard
      </h2>
      <p class="text-muted mb-0">Tüm şirketlerin cache durumunu izleyin ve yönetin</p>
    </div>
    
    <div class="d-flex gap-2">
      <!-- Alerts Toggle -->
      <button
        class="btn btn-sm position-relative"
        [class.btn-warning]="alertsEnabled && getCriticalAlertCount() > 0"
        [class.btn-success]="alertsEnabled && getCriticalAlertCount() === 0"
        [class.btn-outline-secondary]="!alertsEnabled"
        (click)="toggleAlerts()"
        title="Cache Alerts">
        <i class="fas fa-bell"></i>
        <span *ngIf="getCriticalAlertCount() > 0"
              class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
          {{ getCriticalAlertCount() }}
        </span>
      </button>

      <!-- Auto Refresh Toggle -->
      <button
        class="btn btn-sm"
        [class.btn-success]="autoRefresh"
        [class.btn-outline-secondary]="!autoRefresh"
        (click)="toggleAutoRefresh()"
        title="Otomatik Yenileme">
        <i class="fas fa-sync-alt" [class.fa-spin]="autoRefresh"></i>
      </button>

      <!-- Manual Refresh -->
      <button
        class="btn btn-primary btn-sm"
        (click)="loadAllCompaniesStatistics()"
        [disabled]="isLoading">
        <i class="fas fa-refresh" [class.fa-spin]="isLoading"></i>
        Yenile
      </button>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading && !allCompaniesStats" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Yükleniyor...</span>
    </div>
    <p class="mt-2 text-muted">Cache istatistikleri yükleniyor...</p>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading || allCompaniesStats">
    
    <!-- Navigation Tabs -->
    <ul class="nav nav-tabs mb-4">
      <li class="nav-item">
        <button
          class="nav-link"
          [class.active]="activeView === 'overview'"
          (click)="activeView = 'overview'">
          <i class="fas fa-chart-pie me-2"></i>Genel Bakış
        </button>
      </li>
      <li class="nav-item">
        <button
          class="nav-link"
          [class.active]="activeView === 'companies'"
          (click)="activeView = 'companies'">
          <i class="fas fa-building me-2"></i>Şirketler
        </button>
      </li>
      <li class="nav-item">
        <button
          class="nav-link position-relative"
          [class.active]="activeView === 'alerts'"
          (click)="activeView = 'alerts'">
          <i class="fas fa-bell me-2"></i>Alerts
          <span *ngIf="getCriticalAlertCount() > 0"
                class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
            {{ getCriticalAlertCount() }}
          </span>
        </button>
      </li>
      <li class="nav-item">
        <button
          class="nav-link"
          [class.active]="activeView === 'benchmark'"
          (click)="activeView = 'benchmark'">
          <i class="fas fa-trophy me-2"></i>Performance Benchmark
        </button>
      </li>
    </ul>

    <!-- Overview Tab -->
    <div *ngIf="activeView === 'overview'" class="tab-content">
      <div class="row mb-4">
        <!-- Total Companies -->
        <div class="col-md-3 mb-3">
          <div class="card bg-primary text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h6 class="card-title">Toplam Şirket</h6>
                  <h3 class="mb-0">{{ totalCompanies }}</h3>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-building fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Total Cache Keys -->
        <div class="col-md-3 mb-3">
          <div class="card bg-success text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h6 class="card-title">Toplam Cache Key</h6>
                  <h3 class="mb-0">{{ totalCacheKeys | number }}</h3>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-key fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Total Memory Usage -->
        <div class="col-md-3 mb-3">
          <div class="card bg-warning text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h6 class="card-title">Toplam Memory</h6>
                  <h3 class="mb-0">{{ formatMemory(totalMemoryUsage) }}</h3>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-memory fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Average Keys Per Company -->
        <div class="col-md-3 mb-3">
          <div class="card bg-info text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h6 class="card-title">Ortalama Key/Şirket</h6>
                  <h3 class="mb-0">{{ averageKeysPerCompany | number:'1.0-0' }}</h3>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-chart-bar fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Top Companies by Cache Usage -->
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fas fa-trophy me-2"></i>
            En Yüksek Cache Kullanımı
          </h5>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-sm">
              <thead>
                <tr>
                  <th>Şirket</th>
                  <th>Cache Keys</th>
                  <th>Memory Kullanımı</th>
                  <th>Durum</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let company of getTopCompaniesByMemoryUsage().slice(0, 10); let i = index">
                  <td>
                    <strong>{{ company.companyName }}</strong>
                    <span *ngIf="i < 3" class="ms-2">
                      <i class="fas fa-trophy"
                         [class.text-warning]="i === 0"
                         [class.text-secondary]="i === 1"
                         [class.text-dark]="i === 2"></i>
                    </span>
                  </td>
                  <td>
                    <span class="badge bg-secondary">{{ company.statistics.totalKeys | number }}</span>
                  </td>
                  <td>
                    <strong [class.text-warning]="i === 0" [class.text-secondary]="i === 1" [class.text-dark]="i === 2">
                      {{ formatMemory(company.statistics.totalMemoryUsage) }}
                    </strong>
                  </td>
                  <td>
                    <span class="badge"
                          [class.bg-success]="company.statistics.totalKeys > 0"
                          [class.bg-secondary]="company.statistics.totalKeys === 0">
                      {{ company.statistics.totalKeys > 0 ? 'Aktif' : 'Boş' }}
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Companies Tab -->
    <div *ngIf="activeView === 'companies'" class="tab-content">
      
      <!-- Search and Filter Controls -->
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="input-group">
            <span class="input-group-text">
              <i class="fas fa-search"></i>
            </span>
            <input 
              type="text" 
              class="form-control" 
              placeholder="Şirket adı ile ara..."
              [(ngModel)]="searchTerm">
          </div>
        </div>
        <div class="col-md-6">
          <div class="d-flex gap-2">
            <select class="form-select" [(ngModel)]="sortBy" (change)="changeSorting(sortBy)">
              <option value="companyName">Şirket Adı</option>
              <option value="totalKeys">Cache Key Sayısı</option>
              <option value="memoryUsage">Memory Kullanımı</option>
            </select>
            <button 
              class="btn btn-outline-secondary"
              (click)="changeSorting(sortBy)"
              title="Sıralama Yönü">
              <i class="fas" [class.fa-sort-up]="sortDirection === 'asc'" [class.fa-sort-down]="sortDirection === 'desc'"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Bulk Selection Controls -->
      <div class="d-flex justify-content-between align-items-center mb-3">
        <div class="form-check">
          <input 
            class="form-check-input" 
            type="checkbox" 
            id="selectAllCompanies"
            [checked]="selectAll"
            (change)="toggleSelectAll()">
          <label class="form-check-label" for="selectAllCompanies">
            Tümünü Seç
            <span *ngIf="selectedCompanies.size > 0" class="text-muted">
              ({{ selectedCompanies.size }} seçili)
            </span>
          </label>
        </div>
        
        <div *ngIf="selectedCompanies.size > 0">
          <button
            class="btn btn-danger btn-sm"
            (click)="clearSelectedCompaniesCache()"
            [disabled]="isLoading">
            <i class="fas fa-trash me-2"></i>
            Seçili Cache'leri Temizle ({{ selectedCompanies.size }})
          </button>
        </div>
      </div>

      <!-- Companies Table -->
      <div class="card">
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th width="50">
                    <input 
                      type="checkbox" 
                      [checked]="selectAll"
                      (change)="toggleSelectAll()">
                  </th>
                  <th>Şirket</th>
                  <th>Cache Keys</th>
                  <th>Memory</th>
                  <th>Durum</th>
                  <th>Son Güncelleme</th>
                  <th width="120">İşlemler</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let company of getFilteredCompanies()">
                  <td>
                    <input 
                      type="checkbox" 
                      [checked]="selectedCompanies.has(company.companyId)"
                      (change)="toggleCompanySelection(company.companyId)">
                  </td>
                  <td>
                    <div>
                      <strong>{{ company.companyName }}</strong>
                      <br>
                      <small class="text-muted">ID: {{ company.companyId }}</small>
                    </div>
                  </td>
                  <td>
                    <span class="badge bg-primary">{{ company.statistics.totalKeys | number }}</span>
                  </td>
                  <td>{{ formatMemory(company.statistics.totalMemoryUsage) }}</td>
                  <td>
                    <span class="badge"
                          [class.bg-success]="company.statistics.totalKeys > 0"
                          [class.bg-secondary]="company.statistics.totalKeys === 0">
                      {{ company.statistics.totalKeys > 0 ? 'Aktif' : 'Boş' }}
                    </span>
                  </td>
                  <td>
                    <small class="text-muted">{{ company.statistics.lastUpdated }}</small>
                  </td>
                  <td>
                    <div class="btn-group btn-group-sm">
                      <button
                        class="btn btn-outline-info"
                        (click)="viewCompanyDetails(company.companyId, company.companyName)"
                        title="Detayları Görüntüle">
                        <i class="fas fa-eye"></i>
                      </button>
                      <button 
                        class="btn btn-outline-danger"
                        (click)="clearCompanyCache(company.companyId, company.companyName)"
                        title="Cache Temizle">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Alerts Tab -->
    <div *ngIf="activeView === 'alerts'" class="tab-content">
      <div class="row">
        <!-- Alerts Settings -->
        <div class="col-md-4">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="fas fa-cog me-2"></i>
                Alert Ayarları
              </h5>
            </div>
            <div class="card-body">
              <div class="form-check form-switch mb-3">
                <input
                  class="form-check-input"
                  type="checkbox"
                  id="alertsToggle"
                  [checked]="alertsEnabled"
                  (change)="toggleAlerts()">
                <label class="form-check-label" for="alertsToggle">
                  Cache Alerts Aktif
                </label>
              </div>

              <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <small>
                  Alerts aktif olduğunda cache performans sorunları için otomatik uyarılar alırsınız.
                </small>
              </div>
            </div>
          </div>
        </div>

        <!-- Active Alerts -->
        <div class="col-md-8">
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h5 class="mb-0">
                <i class="fas fa-bell me-2"></i>
                Aktif Alerts ({{ alerts.length }})
              </h5>
              <span class="badge bg-danger" *ngIf="getCriticalAlertCount() > 0">
                {{ getCriticalAlertCount() }} Kritik
              </span>
            </div>
            <div class="card-body">
              <div *ngIf="!alertsEnabled" class="text-center py-4">
                <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                <p class="text-muted">Cache alerts kapalı</p>
                <button class="btn btn-primary btn-sm" (click)="toggleAlerts()">
                  Alerts'i Aç
                </button>
              </div>

              <div *ngIf="alertsEnabled && alerts.length === 0" class="text-center py-4">
                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                <p class="text-muted">Tüm sistemler normal çalışıyor</p>
              </div>

              <!-- Alert Actions -->
              <div *ngIf="alertsEnabled && alerts.length > 0" class="mb-3">
                <div class="d-flex justify-content-between align-items-center">
                  <h6 class="mb-0">Aktif Alerts ({{ alerts.length }})</h6>
                  <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-warning" (click)="markAllAlertsAsRead()"
                            [disabled]="alerts.length === 0">
                      <i class="fas fa-check-double me-1"></i>
                      Tümünü Okundu İşaretle
                    </button>
                    <button class="btn btn-outline-danger" (click)="clearAllAlerts()"
                            [disabled]="alerts.length === 0">
                      <i class="fas fa-trash me-1"></i>
                      Tümünü Sil
                    </button>
                  </div>
                </div>
              </div>

              <div *ngIf="alertsEnabled && alerts.length > 0" class="alerts-list">
                <div
                  *ngFor="let alert of alerts.slice(0, 10); let i = index"
                  class="alert mb-2 position-relative"
                  [class.alert-danger]="alert.severity === 'critical'"
                  [class.alert-warning]="alert.severity === 'high' || alert.severity === 'medium'"
                  [class.alert-info]="alert.severity === 'low'">
                  <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1" (click)="markAlertAsRead(alert)" style="cursor: pointer;">
                      <h6 class="alert-heading mb-1">{{ alert.title }}</h6>
                      <p class="mb-1">{{ alert.message }}</p>
                      <small class="text-muted">
                        <i class="fas fa-building me-1"></i>
                        {{ alert.companyName }} - {{ alert.timestamp | date:'dd/MM/yyyy HH:mm' }}
                      </small>
                    </div>
                    <div class="d-flex align-items-center gap-2">
                      <span class="badge"
                            [class.bg-danger]="alert.severity === 'critical'"
                            [class.bg-warning]="alert.severity === 'high'"
                            [class.bg-info]="alert.severity === 'medium' || alert.severity === 'low'">
                        {{ alert.severity.toUpperCase() }}
                      </span>
                      <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-success btn-sm"
                                (click)="markAlertAsRead(alert)"
                                title="Okundu işaretle"
                                *ngIf="!alert.isRead">
                          <i class="fas fa-check"></i>
                        </button>
                        <button class="btn btn-outline-danger btn-sm"
                                (click)="deleteAlert(alert.id)"
                                title="Alert'i sil">
                          <i class="fas fa-times"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <div *ngIf="alerts.length > 10" class="text-center mt-3">
                  <small class="text-muted">
                    {{ alerts.length - 10 }} alert daha var...
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Benchmark Tab -->
    <div *ngIf="activeView === 'benchmark'" class="tab-content">
      <div class="row mb-4">
        <!-- Benchmark Filters -->
        <div class="col-md-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="fas fa-filter me-2"></i>
                Performance Filtreler & Kontrol
              </h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-4">
                  <label class="form-label">Minimum Memory Usage</label>
                  <input type="range" class="form-range" min="0" max="1000"
                         [(ngModel)]="benchmarkFilters.minMemoryUsage">
                  <small class="text-muted">{{ benchmarkFilters.minMemoryUsage }} MB</small>
                </div>
                <div class="col-md-4">
                  <label class="form-label">Maximum Memory Usage</label>
                  <input type="range" class="form-range" min="0" max="1000"
                         [(ngModel)]="benchmarkFilters.maxMemoryUsage">
                  <small class="text-muted">{{ benchmarkFilters.maxMemoryUsage }} MB</small>
                </div>
                <div class="col-md-4">
                  <label class="form-label">Minimum Key Count</label>
                  <input type="range" class="form-range" min="0" max="10000"
                         [(ngModel)]="benchmarkFilters.minKeyCount">
                  <small class="text-muted">{{ benchmarkFilters.minKeyCount }} keys</small>
                </div>
              </div>
              <div class="row mt-3">
                <div class="col-md-4">
                  <label class="form-label">Sıralama</label>
                  <select class="form-select" [(ngModel)]="benchmarkSort">
                    <option value="memoryUsage">Memory Usage</option>
                    <option value="keyCount">Key Count</option>
                    <option value="companyName">Company Name</option>
                  </select>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                  <button class="btn btn-primary" (click)="applyBenchmarkFilters()" [disabled]="isLoading">
                    <i class="fas fa-search me-2"></i>Ara
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Benchmark Rankings -->
      <div class="row">
        <div class="col-md-12">
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <div>
                <h5 class="mb-0">
                  <i class="fas fa-trophy me-2"></i>
                  Performance Sıralaması
                </h5>
                <small class="text-muted">
                  <i class="fas fa-clock me-1"></i>
                  Son güncelleme: {{ getCurrentTime() }}
                </small>
              </div>
              <button class="btn btn-outline-success btn-sm" (click)="exportBenchmarkData()">
                <i class="fas fa-download me-2"></i>Export
              </button>
            </div>
            <div class="card-body">
              <div *ngIf="filteredBenchmarkData.length === 0" class="text-center py-4">
                <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                <p class="text-muted">Benchmark verisi bulunamadı</p>
                <p class="text-muted small">Cache Dashboard'a girdiğinizde veriler otomatik yüklenir</p>
              </div>

              <div *ngIf="filteredBenchmarkData.length > 0" class="table-responsive">
                <table class="table table-hover">
                  <thead>
                    <tr>
                      <th>Sıra</th>
                      <th>Şirket</th>
                      <th>Memory Usage</th>
                      <th>Key Count</th>
                      <th>Durum</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let company of filteredBenchmarkData; let i = index"
                        [class.table-success]="i < 3"
                        [class.table-warning]="i >= filteredBenchmarkData.length - 3">
                      <td>
                        <span class="badge"
                              [class.bg-warning]="i === 0"
                              [class.bg-secondary]="i === 1"
                              [class.bg-dark]="i === 2"
                              [class.bg-light]="i > 2"
                              [class.text-dark]="i > 2">
                          {{ i + 1 }}
                        </span>
                      </td>
                      <td>
                        <strong>{{ company.companyName }}</strong>
                        <br><small class="text-muted">ID: {{ company.companyId }}</small>
                      </td>
                      <td>
                        <span [class.text-warning]="company.memoryUsage > 500"
                              [class.text-danger]="company.memoryUsage > 1000">
                          {{ formatMemoryMB(company.memoryUsage) }}
                        </span>
                      </td>
                      <td>
                        <span [class.text-success]="company.keyCount > 1000"
                              [class.text-warning]="company.keyCount < 100">
                          {{ company.keyCount | number }}
                        </span>
                      </td>
                      <td>
                        <span class="badge"
                              [class.bg-success]="company.keyCount > 0"
                              [class.bg-secondary]="company.keyCount === 0">
                          {{ company.keyCount > 0 ? 'Aktif' : 'Boş' }}
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>

<!-- Company Details Modal -->
<div class="modal fade company-details-modal" [class.show]="showCompanyDetailsModal" [style.display]="showCompanyDetailsModal ? 'block' : 'none'"
     tabindex="-1" role="dialog" aria-labelledby="companyDetailsModalLabel" aria-hidden="true" (click)="closeCompanyDetailsModal()">
  <div class="modal-dialog modal-xl" role="document" (click)="$event.stopPropagation()">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="companyDetailsModalLabel">
          <i class="fas fa-building me-2"></i>
          {{ selectedCompanyDetails?.companyName }} - Cache Detayları
        </h5>
      </div>
      <div class="modal-body" *ngIf="selectedCompanyDetails">

        <div class="row">
          <!-- Company Info -->
          <div class="col-md-4">
            <div class="card">
              <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Şirket Bilgileri</h6>
              </div>
              <div class="card-body">
                <p><strong>Company ID:</strong> {{ selectedCompanyDetails?.details?.company?.companyID || selectedCompanyDetails?.companyId }}</p>
                <p><strong>Şirket Adı:</strong> {{ selectedCompanyDetails?.details?.company?.companyName || selectedCompanyDetails?.companyName }}</p>
                <p><strong>Aktif:</strong>
                  <span class="badge bg-success" *ngIf="selectedCompanyDetails?.details?.company?.isActive">Aktif</span>
                  <span class="badge bg-danger" *ngIf="selectedCompanyDetails?.details?.company?.isActive === false">Pasif</span>
                  <span class="badge bg-secondary" *ngIf="selectedCompanyDetails?.details?.company?.isActive === undefined">Bilinmiyor</span>
                </p>
                <p><strong>Oluşturma Tarihi:</strong> {{ selectedCompanyDetails?.details?.company?.creationDate | date:'dd/MM/yyyy' }}</p>
              </div>
            </div>
          </div>

          <!-- Cache Statistics -->
          <div class="col-md-8">
            <div class="card">
              <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Cache İstatistikleri</h6>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-3">
                    <div class="text-center">
                      <h4 class="text-primary">{{ selectedCompanyDetails?.details?.cacheDetails?.statistics?.totalKeys || 0 }}</h4>
                      <small class="text-muted">Toplam Key</small>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="text-center">
                      <h4 class="text-info">{{ selectedCompanyDetails?.details?.cacheDetails?.statistics?.totalMemoryUsageMB || 0 }} MB</h4>
                      <small class="text-muted">Bellek Kullanımı</small>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="text-center">
                      <h4 class="text-success">{{ selectedCompanyDetails?.details?.cacheDetails?.health?.status || 'Healthy' }}</h4>
                      <small class="text-muted">Durum</small>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="text-center">
                      <h4 class="text-warning">{{ selectedCompanyDetails?.details?.cacheDetails?.health?.responseTime || 0 }}ms</h4>
                      <small class="text-muted">Yanıt Süresi</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Entity Breakdown & Cache Patterns -->
        <div class="row mt-3">
          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-layer-group me-2"></i>Entity Bazlı Dağılım</h6>
              </div>
              <div class="card-body">
                <div *ngFor="let entity of getEntityBreakdown()" class="mb-2">
                  <div class="d-flex justify-content-between align-items-center">
                    <span class="fw-bold">{{ entity.name }}</span>
                    <span class="badge bg-primary">{{ entity.count }} key</span>
                  </div>
                </div>
                <div *ngIf="getEntityBreakdown().length === 0" class="text-muted text-center">
                  <i class="fas fa-info-circle me-2"></i>Entity bilgisi bulunamadı
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-code me-2"></i>Cache Pattern'leri</h6>
              </div>
              <div class="card-body">
                <div *ngFor="let pattern of selectedCompanyDetails?.details?.cacheDetails?.cachePatterns" class="mb-2">
                  <code class="d-block p-2 cache-pattern-code rounded small">{{ pattern }}</code>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


