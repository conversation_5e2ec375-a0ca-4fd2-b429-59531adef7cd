using System;
using System.Collections.Generic;

namespace Entities.DTOs
{
    /// <summary>
    /// Cache warmup request DTO
    /// </summary>
    public class CacheWarmupRequestDto
    {
        public bool WarmupMembers { get; set; } = true;
        public bool WarmupPayments { get; set; } = true;
        public bool WarmupMemberships { get; set; } = true;
        public bool WarmupUsers { get; set; } = false;
        public bool WarmupCompanySettings { get; set; } = false;
    }

    /// <summary>
    /// Cache warmup sonucu DTO
    /// </summary>
    public class CacheWarmupResultDto
    {
        public int CompanyId { get; set; }
        public long TotalDuration { get; set; }
        public List<CacheWarmupItemResultDto> Results { get; set; } = new List<CacheWarmupItemResultDto>();
        public DateTime CompletedAt { get; set; }
    }

    /// <summary>
    /// Cache warmup item sonucu DTO
    /// </summary>
    public class CacheWarmupItemResultDto
    {
        public string Entity { get; set; }
        public string Status { get; set; }
        public string Duration { get; set; }
        public string Message { get; set; }
    }

    /// <summary>
    /// Real-time cache metrics DTO
    /// </summary>
    public class CacheRealtimeMetricsDto
    {
        public int CompanyId { get; set; }
        public DateTime Timestamp { get; set; }
        public CacheStatisticsDto Statistics { get; set; }
        public CachePerformanceDto Performance { get; set; }
        public CacheServerInfoDto ServerInfo { get; set; }
        public CacheTopKeyDto[] TopCacheKeys { get; set; }
    }

    /// <summary>
    /// Cache performance DTO
    /// </summary>
    public class CachePerformanceDto
    {
        public long ResponseTime { get; set; }
        public bool IsConnected { get; set; }
        public int ConnectionCount { get; set; }
    }

    /// <summary>
    /// Cache top key DTO
    /// </summary>
    public class CacheTopKeyDto
    {
        public string Key { get; set; }
        public double TTL { get; set; }
        public string Type { get; set; }
    }
}
