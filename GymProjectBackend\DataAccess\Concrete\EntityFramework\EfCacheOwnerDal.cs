using Core.CrossCuttingConcerns.Caching;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.DTOs;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;


namespace DataAccess.Concrete.EntityFramework
{
    /// <summary>
    /// Cache Owner Data Access Layer Implementation - SOLID prensiplerine uygun
    /// Single Responsibility: Sadece Redis cache data access operasyonları
    /// Open/Closed: Yeni cache operasyonları için genişletilebilir
    /// Liskov Substitution: ICacheOwnerDal interface'ini tam implement eder
    /// Interface Segregation: Sadece cache DAL operasyonları
    /// Dependency Inversion: Redis abstraction'ları kullanır
    /// </summary>
    public class EfCacheOwnerDal : ICacheOwnerDal
    {
        private readonly ICacheService _cacheService;
        private readonly IConnectionMultiplexer _connectionMultiplexer;
        public EfCacheOwnerDal(
            ICacheService cacheService,
            IConnectionMultiplexer connectionMultiplexer)
        {
            _cacheService = cacheService;
            _connectionMultiplexer = connectionMultiplexer;
        }

        #region Company Cache Statistics

        public async Task<CacheStatisticsDto> GetCompanyCacheStatisticsAsync(int companyId)
        {
            var database = _connectionMultiplexer.GetDatabase();
            var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());

            var pattern = $"gym:{companyId}:*";
            var keys = server.Keys(pattern: pattern).ToList();

            var totalKeys = keys.Count;
            var totalMemoryUsage = 0L;
            var keysByEntity = new Dictionary<string, int>();

            foreach (var key in keys)
            {
                try
                {
                    // Memory usage hesapla
                    var keyValue = await database.StringGetAsync(key);
                    if (keyValue.HasValue)
                    {
                        var keySize = System.Text.Encoding.UTF8.GetByteCount(key.ToString());
                        var valueSize = System.Text.Encoding.UTF8.GetByteCount(keyValue.ToString());
                        var overhead = 64; // Redis key overhead
                        totalMemoryUsage += keySize + valueSize + overhead;
                    }

                    // Entity bazlı gruplandırma
                    var keyParts = key.ToString().Split(':');
                    if (keyParts.Length >= 3)
                    {
                        var entity = keyParts[2];
                        keysByEntity[entity] = keysByEntity.GetValueOrDefault(entity, 0) + 1;
                    }
                }
                catch
                {
                    // Hata durumunda devam et
                }
            }

            return new CacheStatisticsDto
            {
                CompanyId = companyId,
                TotalKeys = totalKeys,
                TotalMemoryUsage = totalMemoryUsage,
                TotalMemoryUsageMB = Math.Round(totalMemoryUsage / 1024.0 / 1024.0, 2),
                KeysByEntity = keysByEntity,
                LastUpdated = DateTime.UtcNow
            };
        }

        public async Task<MultiCompanyCacheStatisticsDto> GetAllCompaniesStatisticsAsync()
        {
            // Bu method business layer'da implement edilmeli
            // DAL layer'da company service'e dependency olmamalı
            throw new NotImplementedException("Bu operasyon business layer'da yapılmalıdır");
        }

        #endregion

        #region Cache Health & Monitoring

        public async Task<CacheHealthDto> GetCacheHealthAsync()
        {
            var stopwatch = Stopwatch.StartNew();
            var database = _connectionMultiplexer.GetDatabase();

            var pingTime = await database.PingAsync();
            stopwatch.Stop();

            var serverInfo = await GetRedisServerInfoAsync();

            return new CacheHealthDto
            {
                IsConnected = _connectionMultiplexer.IsConnected,
                PingTime = pingTime.TotalMilliseconds,
                ResponseTime = stopwatch.ElapsedMilliseconds,
                Status = _connectionMultiplexer.IsConnected ? "Healthy" : "Unhealthy",
                ServerInfo = serverInfo
            };
        }

        public async Task<CacheServerInfoDto> GetRedisServerInfoAsync()
        {
            try
            {
                var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
                var info = await server.InfoAsync();

                var serverInfo = info.FirstOrDefault(i => i.Key == "Server");
                var memoryInfo = info.FirstOrDefault(i => i.Key == "Memory");

                var version = serverInfo?.FirstOrDefault(s => s.Key == "redis_version");
                var uptime = serverInfo?.FirstOrDefault(s => s.Key == "uptime_in_seconds");
                var usedMem = memoryInfo?.FirstOrDefault(m => m.Key == "used_memory");
                var usedMemHuman = memoryInfo?.FirstOrDefault(m => m.Key == "used_memory_human");
                var maxMem = memoryInfo?.FirstOrDefault(m => m.Key == "maxmemory");
                var maxMemHuman = memoryInfo?.FirstOrDefault(m => m.Key == "maxmemory_human");

                return new CacheServerInfoDto
                {
                    Version = version?.Value.ToString() ?? "Unknown",
                    UptimeInSeconds = uptime?.Value.ToString() ?? "0",
                    UsedMemory = usedMem?.Value.ToString() ?? "0",
                    UsedMemoryHuman = usedMemHuman?.Value.ToString() ?? "0B",
                    MaxMemory = maxMem?.Value.ToString() ?? "0",
                    MaxMemoryHuman = maxMemHuman?.Value.ToString() ?? "0B"
                };
            }
            catch
            {
                return new CacheServerInfoDto { Version = "Error retrieving server info" };
            }
        }

        public async Task<CacheRealtimeMetricsDto> GetRealtimeCacheMetricsAsync(int companyId)
        {
            var statistics = await GetCompanyCacheStatisticsAsync(companyId);
            var serverInfo = await GetRedisServerInfoAsync();
            var database = _connectionMultiplexer.GetDatabase();

            // Response time test
            var stopwatch = Stopwatch.StartNew();
            await database.PingAsync();
            stopwatch.Stop();

            return new CacheRealtimeMetricsDto
            {
                CompanyId = companyId,
                Timestamp = DateTime.UtcNow,
                Statistics = statistics,
                Performance = new CachePerformanceDto
                {
                    ResponseTime = stopwatch.ElapsedMilliseconds,
                    IsConnected = _connectionMultiplexer.IsConnected,
                    ConnectionCount = (int)_connectionMultiplexer.GetCounters().Interactive.SocketCount
                },
                ServerInfo = serverInfo,
                TopCacheKeys = await GetTopCacheKeysAsync(companyId, 10)
            };
        }

        #endregion

        #region Cache Key Operations

        public async Task<CacheKeysResponseDto> GetKeysByPatternAsync(string pattern, int page, int size)
        {
            var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
            var database = _connectionMultiplexer.GetDatabase();

            var allKeys = server.Keys(pattern: pattern).ToList();
            var totalCount = allKeys.Count;
            var totalPages = (int)Math.Ceiling((double)totalCount / size);

            var pagedKeys = allKeys
                .Skip((page - 1) * size)
                .Take(size)
                .ToList();

            var keyDetails = new List<CacheKeyDetailDto>();

            foreach (var key in pagedKeys)
            {
                try
                {
                    var ttl = await database.KeyTimeToLiveAsync(key);
                    var type = await database.KeyTypeAsync(key);
                    var keyValue = await database.StringGetAsync(key);

                    keyDetails.Add(new CacheKeyDetailDto
                    {
                        Key = key.ToString(),
                        Type = type.ToString(),
                        TTL = ttl?.TotalSeconds,
                        MemoryUsage = keyValue.HasValue ? keyValue.ToString().Length : 0,
                        CreatedAt = DateTime.UtcNow.AddSeconds(-(ttl?.TotalSeconds ?? 0))
                    });
                }
                catch
                {
                    keyDetails.Add(new CacheKeyDetailDto
                    {
                        Key = key.ToString(),
                        Type = "Unknown",
                        TTL = null,
                        MemoryUsage = 0,
                        CreatedAt = null
                    });
                }
            }

            return new CacheKeysResponseDto
            {
                Keys = keyDetails,
                Pagination = new CachePaginationDto
                {
                    CurrentPage = page,
                    PageSize = size,
                    TotalCount = totalCount,
                    TotalPages = totalPages,
                    HasNextPage = page < totalPages,
                    HasPreviousPage = page > 1
                },
                Pattern = pattern
            };
        }

        public async Task<CacheTopKeyDto[]> GetTopCacheKeysAsync(int companyId, int count)
        {
            var pattern = $"gym:{companyId}:*";
            var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
            var keys = server.Keys(pattern: pattern).Take(count).ToArray();

            var topKeys = new List<CacheTopKeyDto>();
            foreach (var key in keys)
            {
                var ttl = await _connectionMultiplexer.GetDatabase().KeyTimeToLiveAsync(key);
                var type = await _connectionMultiplexer.GetDatabase().KeyTypeAsync(key);

                topKeys.Add(new CacheTopKeyDto
                {
                    Key = key.ToString(),
                    TTL = ttl?.TotalSeconds ?? -1,
                    Type = type.ToString()
                });
            }

            return topKeys.ToArray();
        }

        public async Task<CacheKeyValueDto> GetCacheKeyValueAsync(string key)
        {
            var database = _connectionMultiplexer.GetDatabase();
            var value = await database.StringGetAsync(key);
            var ttl = await database.KeyTimeToLiveAsync(key);

            return new CacheKeyValueDto
            {
                Key = key,
                Value = value.HasValue ? value.ToString() : null,
                HasValue = value.HasValue,
                TTL = ttl?.TotalSeconds,
                Size = value.HasValue ? System.Text.Encoding.UTF8.GetByteCount(value) : 0
            };
        }

        #endregion

        #region Cache Clear Operations

        public async Task<CacheClearResultDto> ClearCompanyCacheAsync(int companyId)
        {
            var pattern = $"gym:{companyId}:*";
            var removedCount = _cacheService.RemoveByPattern(pattern);

            return new CacheClearResultDto
            {
                RemovedCount = removedCount,
                Pattern = pattern,
                CompanyId = companyId
            };
        }

        public async Task<CacheClearResultDto> ClearCacheByPatternAsync(string pattern)
        {
            var removedCount = _cacheService.RemoveByPattern(pattern);

            return new CacheClearResultDto
            {
                RemovedCount = removedCount,
                Pattern = pattern
            };
        }

        public async Task<CacheKeyDeleteResultDto> DeleteCacheKeyAsync(string key)
        {
            var removed = _cacheService.Remove(key);

            return new CacheKeyDeleteResultDto
            {
                Key = key,
                Removed = removed,
                Message = removed ? "Cache key başarıyla silindi" : "Cache key bulunamadı"
            };
        }

        public async Task<BulkCacheOperationResultDto> BulkClearCompaniesCache(int[] companyIds)
        {
            var results = new List<BulkCacheOperationItemResultDto>();
            var totalRemovedCount = 0L;

            foreach (var companyId in companyIds)
            {
                try
                {
                    // Company validation business layer'da yapılmalı
                    var pattern = $"gym:{companyId}:*";
                    var removedCount = _cacheService.RemoveByPattern(pattern);
                    totalRemovedCount += removedCount;

                    results.Add(new BulkCacheOperationItemResultDto
                    {
                        CompanyId = companyId,
                        CompanyName = $"Company {companyId}",
                        Success = true,
                        RemovedCount = removedCount,
                        Message = $"{removedCount} adet key silindi"
                    });
                }
                catch (Exception ex)
                {
                    results.Add(new BulkCacheOperationItemResultDto
                    {
                        CompanyId = companyId,
                        Success = false,
                        Message = ex.Message
                    });
                }
            }

            return new BulkCacheOperationResultDto
            {
                TotalRemovedCount = totalRemovedCount,
                ProcessedCompanies = companyIds.Length,
                Results = results
            };
        }

        #endregion

        #region Cache Warmup Operations

        public async Task<CacheWarmupResultDto> PerformCacheWarmupAsync(int companyId, CacheWarmupRequestDto request)
        {
            var warmupResults = new List<CacheWarmupItemResultDto>();
            var stopwatch = Stopwatch.StartNew();

            // Temel cache'leri warmup et
            if (request.WarmupMembers)
            {
                warmupResults.Add(new CacheWarmupItemResultDto
                {
                    Entity = "Members",
                    Status = "Completed",
                    Duration = "0ms",
                    Message = "Member cache warmup tamamlandı"
                });
            }

            if (request.WarmupPayments)
            {
                warmupResults.Add(new CacheWarmupItemResultDto
                {
                    Entity = "Payments",
                    Status = "Completed",
                    Duration = "0ms",
                    Message = "Payment cache warmup tamamlandı"
                });
            }

            if (request.WarmupMemberships)
            {
                warmupResults.Add(new CacheWarmupItemResultDto
                {
                    Entity = "Memberships",
                    Status = "Completed",
                    Duration = "0ms",
                    Message = "Membership cache warmup tamamlandı"
                });
            }

            stopwatch.Stop();

            return new CacheWarmupResultDto
            {
                CompanyId = companyId,
                TotalDuration = stopwatch.ElapsedMilliseconds,
                Results = warmupResults,
                CompletedAt = DateTime.UtcNow
            };
        }

        #endregion

        #region Company Cache Details

        public async Task<CacheDetailsDto> GetCompanyCacheDetailsAsync(int companyId)
        {
            var statistics = await GetCompanyCacheStatisticsAsync(companyId);
            var health = await GetCacheHealthAsync();

            return new CacheDetailsDto
            {
                Statistics = statistics,
                Health = health,
                CompanyId = companyId,
                CachePatterns = new[]
                {
                    $"gym:{companyId}:member:*",
                    $"gym:{companyId}:payment:*",
                    $"gym:{companyId}:membership:*",
                    $"gym:{companyId}:user:*",
                    $"gym:{companyId}:company:*"
                }
            };
        }

        #endregion

        #region Security & Validation

        public bool ValidateCacheKeyOwnership(string key, int companyId)
        {
            if (string.IsNullOrWhiteSpace(key))
                return false;

            return key.StartsWith($"gym:{companyId}:");
        }

        public string ValidateAndSecurePattern(string pattern, int companyId)
        {
            if (string.IsNullOrWhiteSpace(pattern))
                return $"gym:{companyId}:*";

            // Security: Company ID kontrolü - sadece kendi company'sine ait pattern'lere izin ver
            if (!pattern.StartsWith($"gym:{companyId}:"))
            {
                pattern = $"gym:{companyId}:{pattern.TrimStart('*')}";
            }

            return pattern;
        }

        #endregion
    }
}
