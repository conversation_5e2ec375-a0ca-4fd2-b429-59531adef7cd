using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.DTOs;
using System.Threading.Tasks;

namespace Business.Concrete
{
    /// <summary>
    /// Cache Owner Manager - SOLID prensiplerine uygun
    /// Single Responsibility: Sadece cache owner business logic
    /// Open/Closed: Yeni cache operasyonları için genişletilebilir
    /// Liskov Substitution: ICacheOwnerService interface'ini tam implement eder
    /// Interface Segregation: Sadece cache owner operasyonları
    /// Dependency Inversion: ICacheOwnerDal abstraction'ını kullanır
    /// </summary>
    public class CacheOwnerManager : ICacheOwnerService
    {
        private readonly ICacheOwnerDal _cacheOwnerDal;
        private readonly ICompanyContext _companyContext;

        public CacheOwnerManager(ICacheOwnerDal cacheOwnerDal, ICompanyContext companyContext)
        {
            _cacheOwnerDal = cacheOwnerDal;
            _companyContext = companyContext;
        }

        #region Company Cache Operations

        [SecuredOperation("owner")]
        public async Task<IDataResult<CacheStatisticsDto>> GetCacheStatisticsAsync(int? companyId = null)
        {
            try
            {
                var targetCompanyId = companyId ?? _companyContext.GetCompanyId();
                if (targetCompanyId <= 0)
                    return new ErrorDataResult<CacheStatisticsDto>("Geçersiz company ID");

                var statistics = await _cacheOwnerDal.GetCompanyCacheStatisticsAsync(targetCompanyId);
                return new SuccessDataResult<CacheStatisticsDto>(statistics, "Cache istatistikleri başarıyla getirildi");
            }
            catch (System.Exception ex)
            {
                return new ErrorDataResult<CacheStatisticsDto>($"Cache istatistikleri alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<CacheDetailsDto>> GetCacheDetailsAsync(int? companyId = null)
        {
            try
            {
                var targetCompanyId = companyId ?? _companyContext.GetCompanyId();
                if (targetCompanyId <= 0)
                    return new ErrorDataResult<CacheDetailsDto>("Geçersiz company ID");

                var details = await _cacheOwnerDal.GetCompanyCacheDetailsAsync(targetCompanyId);
                return new SuccessDataResult<CacheDetailsDto>(details, "Cache detayları başarıyla getirildi");
            }
            catch (System.Exception ex)
            {
                return new ErrorDataResult<CacheDetailsDto>($"Cache detayları alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<CacheClearResultDto>> ClearCompanyCacheAsync(int? companyId = null)
        {
            try
            {
                var targetCompanyId = companyId ?? _companyContext.GetCompanyId();
                if (targetCompanyId <= 0)
                    return new ErrorDataResult<CacheClearResultDto>("Geçersiz company ID");

                var result = await _cacheOwnerDal.ClearCompanyCacheAsync(targetCompanyId);
                return new SuccessDataResult<CacheClearResultDto>(result, 
                    $"Company cache'i başarıyla temizlendi. {result.RemovedCount} adet key silindi");
            }
            catch (System.Exception ex)
            {
                return new ErrorDataResult<CacheClearResultDto>($"Company cache temizlenirken hata oluştu: {ex.Message}");
            }
        }

        #endregion

        #region Cache Key Operations

        [SecuredOperation("owner")]
        public async Task<IDataResult<CacheKeysResponseDto>> GetCacheKeysAsync(int page = 1, int size = 50, int? companyId = null)
        {
            try
            {
                var targetCompanyId = companyId ?? _companyContext.GetCompanyId();
                if (targetCompanyId <= 0)
                    return new ErrorDataResult<CacheKeysResponseDto>("Geçersiz company ID");

                var pattern = $"gym:{targetCompanyId}:*";
                var keys = await _cacheOwnerDal.GetKeysByPatternAsync(pattern, page, size);
                return new SuccessDataResult<CacheKeysResponseDto>(keys, "Cache key'leri başarıyla getirildi");
            }
            catch (System.Exception ex)
            {
                return new ErrorDataResult<CacheKeysResponseDto>($"Cache key'leri alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<CacheKeysResponseDto>> GetCacheKeysByPatternAsync(string pattern, int page = 1, int size = 50, int? companyId = null)
        {
            try
            {
                var targetCompanyId = companyId ?? _companyContext.GetCompanyId();
                if (targetCompanyId <= 0)
                    return new ErrorDataResult<CacheKeysResponseDto>("Geçersiz company ID");

                // Security: Pattern'i güvenli hale getir
                var securePattern = _cacheOwnerDal.ValidateAndSecurePattern(pattern, targetCompanyId);
                var keys = await _cacheOwnerDal.GetKeysByPatternAsync(securePattern, page, size);
                return new SuccessDataResult<CacheKeysResponseDto>(keys, "Pattern'e göre cache key'leri başarıyla getirildi");
            }
            catch (System.Exception ex)
            {
                return new ErrorDataResult<CacheKeysResponseDto>($"Pattern cache key'leri alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<CacheKeyDeleteResultDto>> DeleteCacheKeyAsync(string key)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                    return new ErrorDataResult<CacheKeyDeleteResultDto>("Geçersiz company ID");

                // Security: Key ownership kontrolü
                if (!_cacheOwnerDal.ValidateCacheKeyOwnership(key, companyId))
                    return new ErrorDataResult<CacheKeyDeleteResultDto>("Bu cache key'ine erişim yetkiniz yok");

                var result = await _cacheOwnerDal.DeleteCacheKeyAsync(key);
                return new SuccessDataResult<CacheKeyDeleteResultDto>(result, result.Message);
            }
            catch (System.Exception ex)
            {
                return new ErrorDataResult<CacheKeyDeleteResultDto>($"Cache key silinirken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<CacheKeyValueDto>> GetCacheKeyValueAsync(string key)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                    return new ErrorDataResult<CacheKeyValueDto>("Geçersiz company ID");

                // Security: Key ownership kontrolü
                if (!_cacheOwnerDal.ValidateCacheKeyOwnership(key, companyId))
                    return new ErrorDataResult<CacheKeyValueDto>("Bu cache key'ine erişim yetkiniz yok");

                var result = await _cacheOwnerDal.GetCacheKeyValueAsync(key);
                return new SuccessDataResult<CacheKeyValueDto>(result, "Cache key değeri başarıyla getirildi");
            }
            catch (System.Exception ex)
            {
                return new ErrorDataResult<CacheKeyValueDto>($"Cache key değeri alınırken hata oluştu: {ex.Message}");
            }
        }

        #endregion

        #region Pattern Operations

        [SecuredOperation("owner")]
        public async Task<IDataResult<CacheClearResultDto>> ClearCacheByPatternAsync(string pattern, int? companyId = null)
        {
            try
            {
                var targetCompanyId = companyId ?? _companyContext.GetCompanyId();
                if (targetCompanyId <= 0)
                    return new ErrorDataResult<CacheClearResultDto>("Geçersiz company ID");

                // Security: Pattern'i güvenli hale getir
                var securePattern = _cacheOwnerDal.ValidateAndSecurePattern(pattern, targetCompanyId);
                var result = await _cacheOwnerDal.ClearCacheByPatternAsync(securePattern);
                return new SuccessDataResult<CacheClearResultDto>(result, 
                    $"Pattern cache'i başarıyla temizlendi. {result.RemovedCount} adet key silindi");
            }
            catch (System.Exception ex)
            {
                return new ErrorDataResult<CacheClearResultDto>($"Pattern cache temizlenirken hata oluştu: {ex.Message}");
            }
        }

        #endregion

        #region Health & Monitoring

        [SecuredOperation("owner")]
        public async Task<IDataResult<CacheHealthDto>> GetCacheHealthAsync()
        {
            try
            {
                var health = await _cacheOwnerDal.GetCacheHealthAsync();
                return new SuccessDataResult<CacheHealthDto>(health, "Cache sağlık durumu başarıyla kontrol edildi");
            }
            catch (System.Exception ex)
            {
                return new ErrorDataResult<CacheHealthDto>($"Cache sağlık kontrolü yapılırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<CacheRealtimeMetricsDto>> GetRealtimeMetricsAsync(int? companyId = null)
        {
            try
            {
                var targetCompanyId = companyId ?? _companyContext.GetCompanyId();
                if (targetCompanyId <= 0)
                    return new ErrorDataResult<CacheRealtimeMetricsDto>("Geçersiz company ID");

                var metrics = await _cacheOwnerDal.GetRealtimeCacheMetricsAsync(targetCompanyId);
                return new SuccessDataResult<CacheRealtimeMetricsDto>(metrics, "Real-time cache metrics başarıyla getirildi");
            }
            catch (System.Exception ex)
            {
                return new ErrorDataResult<CacheRealtimeMetricsDto>($"Real-time metrics alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<CacheWarmupResultDto>> PerformCacheWarmupAsync(CacheWarmupRequestDto request, int? companyId = null)
        {
            try
            {
                var targetCompanyId = companyId ?? _companyContext.GetCompanyId();
                if (targetCompanyId <= 0)
                    return new ErrorDataResult<CacheWarmupResultDto>("Geçersiz company ID");

                var result = await _cacheOwnerDal.PerformCacheWarmupAsync(targetCompanyId, request);
                return new SuccessDataResult<CacheWarmupResultDto>(result, "Cache warmup işlemi başarıyla tamamlandı");
            }
            catch (System.Exception ex)
            {
                return new ErrorDataResult<CacheWarmupResultDto>($"Cache warmup işlemi sırasında hata oluştu: {ex.Message}");
            }
        }

        #endregion

        #region Multi-Company Operations (Owner Only)

        [SecuredOperation("owner")]
        public async Task<IDataResult<MultiCompanyCacheStatisticsDto>> GetAllCompaniesStatisticsAsync()
        {
            try
            {
                var statistics = await _cacheOwnerDal.GetAllCompaniesStatisticsAsync();
                return new SuccessDataResult<MultiCompanyCacheStatisticsDto>(statistics, "Tüm şirket cache istatistikleri başarıyla getirildi");
            }
            catch (System.Exception ex)
            {
                return new ErrorDataResult<MultiCompanyCacheStatisticsDto>($"Cache istatistikleri alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<CompanyCacheDetailsDto>> GetSpecificCompanyCacheDetailsAsync(int companyId)
        {
            try
            {
                var details = await _cacheOwnerDal.GetCompanyCacheDetailsAsync(companyId);

                // Company bilgisini de ekleyelim (bu kısım CompanyService'den gelecek)
                var result = new CompanyCacheDetailsDto
                {
                    CacheDetails = details
                    // Company = companyResult.Data // Bu kısım CompanyService entegrasyonu ile tamamlanacak
                };

                return new SuccessDataResult<CompanyCacheDetailsDto>(result, "Şirket cache detayları başarıyla getirildi");
            }
            catch (System.Exception ex)
            {
                return new ErrorDataResult<CompanyCacheDetailsDto>($"Şirket cache detayları alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<CacheClearResultDto>> ClearSpecificCompanyCacheAsync(int companyId)
        {
            try
            {
                var result = await _cacheOwnerDal.ClearCompanyCacheAsync(companyId);
                return new SuccessDataResult<CacheClearResultDto>(result,
                    $"Şirket cache'i başarıyla temizlendi. {result.RemovedCount} adet key silindi");
            }
            catch (System.Exception ex)
            {
                return new ErrorDataResult<CacheClearResultDto>($"Şirket cache'i temizlenirken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public async Task<IDataResult<BulkCacheOperationResultDto>> BulkClearCompaniesCache(BulkCacheOperationRequestDto request)
        {
            try
            {
                var result = await _cacheOwnerDal.BulkClearCompaniesCache(request.CompanyIds.ToArray());
                return new SuccessDataResult<BulkCacheOperationResultDto>(result,
                    $"Toplu cache temizleme tamamlandı. Toplam {result.TotalRemovedCount} adet key silindi");
            }
            catch (System.Exception ex)
            {
                return new ErrorDataResult<BulkCacheOperationResultDto>($"Toplu cache temizleme sırasında hata oluştu: {ex.Message}");
            }
        }

        #endregion
    }
}
