
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {},
  assets: {
    'index.csr.html': {size: 41548, hash: 'f955fa759db9dbc4fd2ad9a32c44e8731870e1cc5e51440bd5f6acc20ad59349', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 21716, hash: '472c4cae68ed727cd7c359915f5f3872ed82538a425d917931bfbc00df3c79c1', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-V3UH3NNJ.css': {size: 302582, hash: 'KVkHM28HPpc', text: () => import('./assets-chunks/styles-V3UH3NNJ_css.mjs').then(m => m.default)}
  },
};
