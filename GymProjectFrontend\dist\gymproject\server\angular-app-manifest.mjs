
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {},
  assets: {
    'index.csr.html': {size: 41548, hash: 'ae58803da4b667a9c412b6374df843df5e2e9033aafce79718bcc110c9922aea', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 21716, hash: 'a5c7d6fa832254dffb2eb7d5fb2570eb563228556cd61c3d75ac82134a1a6862', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-V3UH3NNJ.css': {size: 302582, hash: 'KVkHM28HPpc', text: () => import('./assets-chunks/styles-V3UH3NNJ_css.mjs').then(m => m.default)}
  },
};
