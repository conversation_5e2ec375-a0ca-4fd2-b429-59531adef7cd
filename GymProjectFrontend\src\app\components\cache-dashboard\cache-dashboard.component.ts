import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CacheAdminService } from '../../services/cache-admin.service';
import { CacheAlertService, CacheAlert } from '../../services/cache-alert.service';
import { AuthService } from '../../services/auth.service';
import { ToastrService } from 'ngx-toastr';
import { Subscription, interval } from 'rxjs';
import { UserModel } from '../../models/userModel';

export interface BenchmarkMetrics {
  companyId: number;
  companyName: string;
  memoryUsage: number; // MB
  keyCount: number;
  rank: number;
}



export interface BenchmarkFilters {
  minMemoryUsage: number;
  maxMemoryUsage: number;
  minKeyCount: number;
}

@Component({
  selector: 'app-cache-dashboard',
  templateUrl: './cache-dashboard.component.html',
  styleUrls: ['./cache-dashboard.component.css'],
  standalone: false
})
export class CacheDashboardComponent implements OnInit, OnDestroy {
  isLoading = false;
  currentUser: UserModel | null = null;

  // Multi-Company Data
  allCompaniesStats: any = null;
  selectedCompanies: Set<number> = new Set();
  selectAll = false;

  // Company Selection & Filtering
  searchTerm = '';
  sortBy = 'companyName'; // companyName, totalKeys, memoryUsage, healthScore
  sortDirection = 'asc';

  // Real-time Updates
  private realtimeSubscription: Subscription | null = null;
  private alertsSubscription: Subscription | null = null;
  autoRefresh = false; // Varsayılan olarak kapalı
  refreshInterval = 10000; // 10 saniye (multi-company için daha uzun)

  // Cache Alerts Integration
  alerts: CacheAlert[] = [];
  criticalAlerts: CacheAlert[] = [];
  alertsEnabled = true;

  // Benchmark Data
  benchmarkData: BenchmarkMetrics[] = [];
  filteredBenchmarkData: BenchmarkMetrics[] = [];
  benchmarkFilters: BenchmarkFilters = {
    minMemoryUsage: 0,
    maxMemoryUsage: 1000,
    minKeyCount: 0
  };
  benchmarkSort = 'memoryUsage';

  // Auto-refresh for benchmark data
  benchmarkAutoRefresh = false;
  benchmarkRefreshInterval: any;

  // UI State
  activeView = 'overview'; // overview, companies, alerts, benchmark

  // Company Details Modal
  showCompanyDetailsModal = false;
  selectedCompanyDetails: any = null;

  // Array helper for template
  Array = Array;

  // Statistics
  totalCompanies = 0;
  totalCacheKeys = 0;
  totalMemoryUsage = 0;
  averageKeysPerCompany = 0;

  constructor(
    private cacheAdminService: CacheAdminService,
    private cacheAlertService: CacheAlertService,
    private authService: AuthService,
    private toastr: ToastrService
  ) {}

  async ngOnInit(): Promise<void> {
    this.currentUser = this.authService.currentUserValue;

    // Yetki kontrolü
    if (!this.currentUser || !this.hasMultiCompanyAccess()) {
      this.toastr.error('Bu sayfaya erişim yetkiniz yok');
      return;
    }

    await this.loadAllCompaniesStatistics();
    this.subscribeToAlerts();
    // startRealtimeUpdates() çağrısı kaldırıldı - kullanıcı manuel olarak açacak
  }

  ngOnDestroy(): void {
    this.stopRealtimeUpdates();
    if (this.alertsSubscription) {
      this.alertsSubscription.unsubscribe();
    }

    // Clear benchmark auto-refresh
    if (this.benchmarkRefreshInterval) {
      clearInterval(this.benchmarkRefreshInterval);
    }
  }

  private hasMultiCompanyAccess(): boolean {
    if (!this.currentUser?.role) return false;

    // Role array veya string olabilir
    const roles = Array.isArray(this.currentUser.role) ? this.currentUser.role : [this.currentUser.role];
    return roles.includes('owner') || roles.includes('admin');
  }

  /**
   * Tüm şirketlerin cache istatistiklerini yükler
   */
  async loadAllCompaniesStatistics(): Promise<void> {
    if (this.isLoading) return;

    this.isLoading = true;
    try {
      const response = await this.cacheAdminService.getAllCompaniesStatistics().toPromise();
      if (response?.success) {
        this.allCompaniesStats = response.data;
        this.updateStatistics();
        this.toastr.success('Şirket cache istatistikleri güncellendi');
      } else {
        this.toastr.error('Cache istatistikleri alınamadı');
      }
    } catch (error) {
      console.error('Cache istatistikleri yüklenirken hata:', error);
      this.toastr.error('Cache istatistikleri yüklenirken hata oluştu');
    } finally {
      this.isLoading = false;
    }
  }

  private updateStatistics(): void {
    if (!this.allCompaniesStats) return;

    this.totalCompanies = this.allCompaniesStats.totalCompanies || 0;
    this.totalCacheKeys = this.allCompaniesStats.totalCacheKeys || 0;
    this.totalMemoryUsage = this.allCompaniesStats.totalMemoryUsage || 0;
    this.averageKeysPerCompany = this.allCompaniesStats.averageKeysPerCompany || 0;

    // Benchmark verilerini de güncelle
    this.updateBenchmarkDataFromStats();
  }

  /**
   * Mevcut statistics verisinden benchmark verilerini güncelle
   */
  private updateBenchmarkDataFromStats(): void {
    if (this.allCompaniesStats) {
      this.benchmarkData = this.transformApiDataToBenchmark(this.allCompaniesStats);
      // Filtreleri otomatik uygulamıyoruz - kullanıcı "Ara" butonuna basacak
      this.filteredBenchmarkData = [...this.benchmarkData]; // Başlangıçta tüm veriyi göster
    }
  }

  /**
   * Cache alerts'leri dinle
   */
  private subscribeToAlerts(): void {
    if (this.alertsEnabled) {
      this.alertsSubscription = this.cacheAlertService.alerts$.subscribe(alerts => {
        this.alerts = alerts;
        this.criticalAlerts = alerts.filter(alert =>
          alert.severity === 'critical' || alert.severity === 'high'
        );
      });
    }
  }

  /**
   * Alert'leri toggle et
   */
  toggleAlerts(): void {
    this.alertsEnabled = !this.alertsEnabled;
    if (this.alertsEnabled) {
      this.subscribeToAlerts();
      this.toastr.info('Cache alerts açıldı');
    } else {
      if (this.alertsSubscription) {
        this.alertsSubscription.unsubscribe();
        this.alertsSubscription = null;
      }
      this.alerts = [];
      this.criticalAlerts = [];
      this.toastr.info('Cache alerts kapatıldı');
    }
  }

  /**
   * Alert'i okundu olarak işaretle
   */
  markAlertAsRead(alert: CacheAlert): void {
    this.cacheAlertService.markAsRead(alert.id);
    this.toastr.success('Alert okundu olarak işaretlendi');
  }

  /**
   * Tüm alert'leri okundu olarak işaretle
   */
  markAllAlertsAsRead(): void {
    this.alerts.forEach(alert => {
      this.cacheAlertService.markAsRead(alert.id);
    });
    this.toastr.success('Tüm alert\'ler okundu olarak işaretlendi');
  }

  /**
   * Belirli bir alert'i sil
   */
  deleteAlert(alertId: string): void {
    if (confirm('Bu alert\'i silmek istediğinizden emin misiniz?')) {
      this.cacheAlertService.deleteAlert(alertId);
      this.toastr.success('Alert silindi');
    }
  }

  /**
   * Tüm alert'leri temizle
   */
  clearAllAlerts(): void {
    if (confirm('Tüm alert\'leri silmek istediğinizden emin misiniz?')) {
      this.cacheAlertService.clearAllAlerts();
      this.toastr.success('Tüm alert\'ler temizlendi');
    }
  }

  /**
   * Kritik alert sayısını al
   */
  getCriticalAlertCount(): number {
    return this.criticalAlerts.length;
  }

  /**
   * Seçili şirketlerin cache'lerini temizle (toplu işlemler)
   */
  async clearSelectedCompaniesCache(): Promise<void> {
    if (this.selectedCompanies.size === 0) {
      this.toastr.warning('Lütfen temizlenecek şirketleri seçin');
      return;
    }

    const selectedCount = this.selectedCompanies.size;
    if (!confirm(`Seçili ${selectedCount} şirketin cache'ini temizlemek istediğinizden emin misiniz?`)) {
      return;
    }

    this.isLoading = true;
    try {
      // Seçili şirket ID'lerini array'e çevir
      const companyIds = Array.from(this.selectedCompanies);

      // Toplu cache temizleme API'sini çağır
      const response = await this.cacheAdminService.bulkClearCompaniesCache(companyIds).toPromise();

      if (response?.success) {
        this.toastr.success(`Seçili ${selectedCount} şirketin cache'i başarıyla temizlendi`);
        this.selectedCompanies.clear();
        this.selectAll = false;
        await this.loadAllCompaniesStatistics(); // Refresh data
      } else {
        this.toastr.error('Cache temizleme işlemi başarısız');
      }
    } catch (error) {
      console.error('Cache temizleme hatası:', error);
      this.toastr.error('Cache temizlenirken hata oluştu');
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Benchmark verilerini mevcut statistics'ten yükle
   */
  loadBenchmarkData(): void {
    this.isLoading = true;
    try {
      if (this.allCompaniesStats) {
        // Mevcut statistics verisini kullan
        this.benchmarkData = this.transformApiDataToBenchmark(this.allCompaniesStats);
        this.filteredBenchmarkData = [...this.benchmarkData]; // Başlangıçta tüm veriyi göster
        this.toastr.success('Benchmark verileri güncellendi');
      } else {
        // Statistics verisi yoksa mock data kullan
        console.warn('Statistics verisi bulunamadı, mock data kullanılıyor');
        this.benchmarkData = this.generateMockBenchmarkData();
        this.filteredBenchmarkData = [...this.benchmarkData];
        this.toastr.info('Demo verileri yüklendi');
      }
    } catch (error) {
      console.error('Benchmark yükleme hatası:', error);
      // Fallback to mock data on error
      this.benchmarkData = this.generateMockBenchmarkData();
      this.filteredBenchmarkData = [...this.benchmarkData];
      this.toastr.warning('Veri işleme hatası - demo verileri gösteriliyor');
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Benchmark filtrelerini uygula
   */
  applyBenchmarkFilters(): void {
    this.filteredBenchmarkData = this.benchmarkData.filter(company =>
      company.memoryUsage >= this.benchmarkFilters.minMemoryUsage &&
      company.memoryUsage <= this.benchmarkFilters.maxMemoryUsage &&
      company.keyCount >= this.benchmarkFilters.minKeyCount
    );

    // Sıralama uygula
    this.filteredBenchmarkData.sort((a, b) => {
      switch (this.benchmarkSort) {
        case 'memoryUsage':
          return b.memoryUsage - a.memoryUsage; // Yüksekten düşüğe
        case 'keyCount':
          return b.keyCount - a.keyCount; // Yüksekten düşüğe
        case 'companyName':
          return a.companyName.localeCompare(b.companyName);
        default:
          return b.memoryUsage - a.memoryUsage;
      }
    });

    // Rank güncelle
    this.filteredBenchmarkData.forEach((company, index) => {
      company.rank = index + 1;
    });
  }



  /**
   * Benchmark verilerini export et
   */
  exportBenchmarkData(): void {
    const csvContent = this.convertBenchmarkToCSV();
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `cache-benchmark-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
    window.URL.revokeObjectURL(url);
    this.toastr.success('Benchmark verileri export edildi');
  }

  /**
   * Real-time güncellemeleri başlatır
   */
  private startRealtimeUpdates(): void {
    if (this.autoRefresh) {
      this.realtimeSubscription = interval(this.refreshInterval).subscribe(() => {
        this.loadAllCompaniesStatistics();
      });
    }
  }

  /**
   * Real-time güncellemeleri durdurur
   */
  private stopRealtimeUpdates(): void {
    if (this.realtimeSubscription) {
      this.realtimeSubscription.unsubscribe();
      this.realtimeSubscription = null;
    }
  }

  /**
   * Auto refresh toggle
   */
  toggleAutoRefresh(): void {
    this.autoRefresh = !this.autoRefresh;
    if (this.autoRefresh) {
      this.startRealtimeUpdates();
      this.toastr.info('Otomatik yenileme açıldı');
    } else {
      this.stopRealtimeUpdates();
      this.toastr.info('Otomatik yenileme kapatıldı');
    }
  }

  /**
   * Şirket seçimi
   */
  toggleCompanySelection(companyId: number): void {
    if (this.selectedCompanies.has(companyId)) {
      this.selectedCompanies.delete(companyId);
    } else {
      this.selectedCompanies.add(companyId);
    }
    this.updateSelectAllState();
  }

  /**
   * Tümünü seç/seçme
   */
  toggleSelectAll(): void {
    this.selectAll = !this.selectAll;
    this.selectedCompanies.clear();
    
    if (this.selectAll && this.allCompaniesStats?.companies) {
      this.allCompaniesStats.companies.forEach((company: any) => {
        this.selectedCompanies.add(company.companyId);
      });
    }
  }

  private updateSelectAllState(): void {
    const totalCompanies = this.allCompaniesStats?.companies?.length || 0;
    this.selectAll = totalCompanies > 0 && this.selectedCompanies.size === totalCompanies;
  }

  /**
   * Genel bakış için şirket listesi - Memory usage'a göre sıralı
   */
  getTopCompaniesByMemoryUsage(): any[] {
    if (!this.allCompaniesStats?.companies) return [];

    let companies = [...this.allCompaniesStats.companies];

    // Memory usage'a göre en yüksekten düşüğe sırala
    companies.sort((a, b) => {
      return b.statistics.totalMemoryUsage - a.statistics.totalMemoryUsage;
    });

    return companies;
  }

  /**
   * Filtrelenmiş şirket listesi (Şirketler sekmesi için)
   */
  getFilteredCompanies(): any[] {
    if (!this.allCompaniesStats?.companies) return [];

    let companies = [...this.allCompaniesStats.companies];

    // Arama filtresi
    if (this.searchTerm) {
      companies = companies.filter(company =>
        company.companyName.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }

    // Sıralama
    companies.sort((a, b) => {
      let aValue, bValue;

      switch (this.sortBy) {
        case 'companyName':
          aValue = a.companyName.toLowerCase();
          bValue = b.companyName.toLowerCase();
          break;
        case 'totalKeys':
          aValue = a.statistics.totalKeys;
          bValue = b.statistics.totalKeys;
          break;
        case 'memoryUsage':
          aValue = a.statistics.totalMemoryUsage;
          bValue = b.statistics.totalMemoryUsage;
          break;
        default:
          aValue = a.companyName.toLowerCase();
          bValue = b.companyName.toLowerCase();
      }

      if (this.sortDirection === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return companies;
  }

  /**
   * Sıralama değiştir
   */
  changeSorting(field: string): void {
    if (this.sortBy === field) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortBy = field;
      this.sortDirection = 'asc';
    }
  }

  /**
   * Şirket cache detaylarını görüntüle
   */
  async viewCompanyDetails(companyId: number, companyName: string): Promise<void> {
    try {
      this.isLoading = true;
      const response = await this.cacheAdminService.getSpecificCompanyCacheDetails(companyId).toPromise();

      if (response?.success) {
        // Modal veya detay paneli açmak için data'yı hazırla
        this.selectedCompanyDetails = {
          companyId: companyId,
          companyName: companyName,
          details: response.data
        };

        this.showCompanyDetailsModal = true;
        this.toastr.success(`${companyName} detayları yüklendi`);
      } else {
        this.toastr.error('Şirket detayları alınamadı');
      }
    } catch (error) {
      console.error('Şirket detayları yüklenirken hata:', error);
      this.toastr.error('Şirket detayları yüklenirken hata oluştu');
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Şirket detayları modal'ını kapat
   */
  closeCompanyDetailsModal(): void {
    this.showCompanyDetailsModal = false;
    this.selectedCompanyDetails = null;
  }

  /**
   * Entity breakdown'unu hesapla
   */
  getEntityBreakdown(): any[] {
    console.log('getEntityBreakdown called');
    console.log('selectedCompanyDetails:', this.selectedCompanyDetails);

    if (!this.selectedCompanyDetails?.details?.cacheDetails?.statistics?.keysByEntity) {
      console.log('keysByEntity not found');
      return [];
    }

    const keysByEntity = this.selectedCompanyDetails.details.cacheDetails.statistics.keysByEntity;
    const totalKeys = this.selectedCompanyDetails.details.cacheDetails.statistics.totalKeys;

    console.log('keysByEntity:', keysByEntity);
    console.log('totalKeys:', totalKeys);

    const result = Object.entries(keysByEntity).map(([entity, count]: [string, any]) => ({
      name: entity,
      count: count,
      percentage: totalKeys > 0 ? (count / totalKeys) * 100 : 0
    })).sort((a, b) => b.count - a.count);

    console.log('Entity breakdown result:', result);
    return result;
  }

  /**
   * Belirli şirketin cache'ini temizle
   */
  async clearCompanyCache(companyId: number, companyName: string): Promise<void> {
    if (!confirm(`${companyName} şirketinin tüm cache'ini temizlemek istediğinizden emin misiniz?`)) {
      return;
    }

    try {
      const response = await this.cacheAdminService.clearSpecificCompanyCache(companyId).toPromise();
      if (response?.success) {
        this.toastr.success(`${companyName} cache'i başarıyla temizlendi`);
        await this.loadAllCompaniesStatistics(); // Refresh data
      } else {
        this.toastr.error('Cache temizleme işlemi başarısız');
      }
    } catch (error) {
      console.error('Cache temizleme hatası:', error);
      this.toastr.error('Cache temizlenirken hata oluştu');
    }
  }

  // Toplu işlemler bölümü kaldırıldı - şirket listesinden direkt yapılabilir

  /**
   * Memory formatı (byte cinsinden)
   */
  formatMemory(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Memory formatı (MB cinsinden - benchmark için)
   */
  formatMemoryMB(megabytes: number): string {
    if (megabytes === 0) return '0 MB';
    if (megabytes < 1) {
      return (megabytes * 1024).toFixed(2) + ' KB';
    }
    if (megabytes >= 1024) {
      return (megabytes / 1024).toFixed(2) + ' GB';
    }
    return megabytes.toFixed(2) + ' MB';
  }







  /**
   * Benchmark verilerini CSV formatına çevir
   */
  private convertBenchmarkToCSV(): string {
    const headers = ['Sıra', 'Şirket ID', 'Şirket Adı', 'Memory Usage (MB)', 'Key Count'];
    const rows = this.filteredBenchmarkData.map((company, index) => [
      index + 1,
      company.companyId,
      company.companyName,
      company.memoryUsage,
      company.keyCount
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }

  /**
   * API verilerini benchmark formatına dönüştür
   */
  private transformApiDataToBenchmark(apiData: any): BenchmarkMetrics[] {
    if (!apiData || !Array.isArray(apiData.companies)) {
      console.warn('API verisi beklenen formatta değil:', apiData);
      return this.generateMockBenchmarkData();
    }

    return apiData.companies.map((company: any, index: number) => {
      // Statistics verisi company.statistics içinde
      const stats = company.statistics || {};

      return {
        companyId: company.companyId || index + 1,
        companyName: company.companyName || `Şirket ${index + 1}`,
        memoryUsage: stats.totalMemoryUsageMB || 0, // MB
        keyCount: stats.totalKeys || 0,
        rank: index + 1
      };
    }).sort((a: any, b: any) => b.memoryUsage - a.memoryUsage) // Memory usage'a göre sırala
      .map((item: any, index: number) => ({ ...item, rank: index + 1 }));
  }



  /**
   * Mock benchmark data oluştur (fallback)
   */
  private generateMockBenchmarkData(): BenchmarkMetrics[] {
    const companies = [
      'Fitness Center A', 'Spor Salonu B', 'Gym Center C', 'Power Gym D',
      'Elite Fitness E', 'Strong Gym F', 'Fit Club G', 'Body Gym H'
    ];

    return companies.map((name, index) => ({
      companyId: index + 1,
      companyName: name,
      memoryUsage: Math.floor(Math.random() * 500) + 100, // 100-600 MB
      keyCount: Math.floor(Math.random() * 10000) + 1000, // 1000-11000 keys
      rank: index + 1
    })).sort((a, b) => b.memoryUsage - a.memoryUsage) // Memory usage'a göre sırala
      .map((item, index) => ({ ...item, rank: index + 1 }));
  }

  /**
   * Benchmark auto-refresh toggle
   */
  toggleBenchmarkAutoRefresh(): void {
    this.benchmarkAutoRefresh = !this.benchmarkAutoRefresh;

    if (this.benchmarkAutoRefresh) {
      this.startBenchmarkAutoRefresh();
      this.toastr.success('Otomatik yenileme başlatıldı (30 saniye)');
    } else {
      this.stopBenchmarkAutoRefresh();
      this.toastr.info('Otomatik yenileme durduruldu');
    }
  }

  /**
   * Start benchmark auto-refresh
   */
  private startBenchmarkAutoRefresh(): void {
    this.stopBenchmarkAutoRefresh(); // Clear existing interval

    this.benchmarkRefreshInterval = setInterval(() => {
      if (this.activeView === 'benchmark') {
        this.loadAllCompaniesStatistics(); // Bu otomatik olarak benchmark'ı da güncelleyecek
      }
    }, 30000); // 30 seconds
  }

  /**
   * Stop benchmark auto-refresh
   */
  private stopBenchmarkAutoRefresh(): void {
    if (this.benchmarkRefreshInterval) {
      clearInterval(this.benchmarkRefreshInterval);
      this.benchmarkRefreshInterval = null;
    }
  }

  /**
   * Manual refresh benchmark data - statistics'i yeniden yükle
   */
  async refreshBenchmarkData(): Promise<void> {
    this.toastr.info('Cache verileri yenileniyor...');
    await this.loadAllCompaniesStatistics(); // Bu otomatik olarak benchmark'ı da güncelleyecek
  }

  /**
   * Get current time for display
   */
  getCurrentTime(): string {
    return new Date().toLocaleTimeString('tr-TR');
  }
}
