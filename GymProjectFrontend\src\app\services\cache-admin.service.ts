import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseApiService } from './baseApiService';
import { ListResponseModel } from '../models/listResponseModel';
import { SingleResponseModel } from '../models/singleResponseModel';
import { ResponseModel } from '../models/responseModel';

// Backend CacheAdminController ile uyumlu interface'ler
export interface CacheStatistics {
  companyId: number;
  totalKeys: number;
  totalMemoryUsage: number;
  totalMemoryUsageMB: number;
  keysByEntity: { [key: string]: number };
  lastUpdated: string;
}

export interface CacheHealth {
  isConnected: boolean;
  pingTime: number;
  responseTime: number;
  status: string;
  serverInfo: {
    version?: string;
    uptimeInSeconds?: string;
    usedMemory?: string;
    usedMemoryHuman?: string;
    maxMemory?: string;
    maxMemoryHuman?: string;
  };
}

export interface CacheKeyDetail {
  key: string;
  type: string;
  ttl?: number;
  memoryUsage: number;
  createdAt?: string;
}

export interface CacheKeysResponse {
  keys: CacheKeyDetail[];
  pagination: {
    currentPage: number;
    pageSize: number;
    totalCount: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  pattern: string;
}

export interface CacheKeyValue {
  key: string;
  value?: string;
  hasValue: boolean;
  ttl?: number;
  size: number;
}

export interface CacheClearResult {
  removedCount: number;
  pattern: string;
}

export interface CacheWarmupRequest {
  warmupMembers?: boolean;
  warmupPayments?: boolean;
  warmupMemberships?: boolean;
  warmupUsers?: boolean;
  warmupCompanySettings?: boolean;
}

export interface CacheWarmupResult {
  companyId: number;
  totalDuration: number;
  results: Array<{
    entity: string;
    status: string;
    duration: string;
  }>;
  completedAt: string;
}

export interface TenantCacheDetails {
  statistics: CacheStatistics;
  health: CacheHealth;
  companyId: number;
  cachePatterns: string[];
}

@Injectable({
  providedIn: 'root'
})
export class CacheAdminService extends BaseApiService {

  constructor(private httpClient: HttpClient) {
    super();
  }

  /**
   * Cache istatistiklerini getirir (Company bazlı)
   */
  getStatistics(): Observable<SingleResponseModel<CacheStatistics>> {
    return this.httpClient.get<SingleResponseModel<CacheStatistics>>(
      `${this.apiUrl}cacheowner/statistics`
    );
  }

  /**
   * Redis bağlantı durumunu kontrol eder
   */
  getHealthInfo(): Observable<SingleResponseModel<CacheHealth>> {
    return this.httpClient.get<SingleResponseModel<CacheHealth>>(
      `${this.apiUrl}cacheowner/health`
    );
  }

  /**
   * Company'ye ait cache key'lerini listeler (pagination ile)
   */
  getCompanyCacheKeys(page: number = 1, size: number = 50): Observable<SingleResponseModel<CacheKeysResponse>> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    return this.httpClient.get<SingleResponseModel<CacheKeysResponse>>(
      `${this.apiUrl}cacheowner/keys`, { params }
    );
  }

  /**
   * Belirli pattern'e göre cache key'lerini listeler
   */
  getKeysByPattern(pattern: string, page: number = 1, size: number = 50): Observable<SingleResponseModel<CacheKeysResponse>> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    return this.httpClient.get<SingleResponseModel<CacheKeysResponse>>(
      `${this.apiUrl}cacheowner/keys/pattern/${encodeURIComponent(pattern)}`, { params }
    );
  }

  /**
   * Company'nin tüm cache'ini temizler
   */
  clearTenantCache(): Observable<SingleResponseModel<CacheClearResult>> {
    return this.httpClient.delete<SingleResponseModel<CacheClearResult>>(
      `${this.apiUrl}cacheowner/clear`
    );
  }

  /**
   * Belirli pattern'deki cache'leri temizler
   */
  clearCacheByPattern(pattern: string): Observable<SingleResponseModel<CacheClearResult>> {
    return this.httpClient.delete<SingleResponseModel<CacheClearResult>>(
      `${this.apiUrl}cacheowner/pattern/clear?pattern=${encodeURIComponent(pattern)}`
    );
  }

  /**
   * Tüm cache'leri temizler (Company cache'i temizler)
   */
  clearAllCache(): Observable<SingleResponseModel<CacheClearResult>> {
    return this.httpClient.delete<SingleResponseModel<CacheClearResult>>(
      `${this.apiUrl}cacheowner/clear`
    );
  }

  /**
   * Company cache'lerini temizler
   */
  clearCompanyCache(): Observable<SingleResponseModel<CacheClearResult>> {
    return this.httpClient.delete<SingleResponseModel<CacheClearResult>>(
      `${this.apiUrl}cacheowner/clear`
    );
  }

  /**
   * Company cache detaylarını getirir
   */
  getTenantCacheDetails(): Observable<SingleResponseModel<TenantCacheDetails>> {
    return this.httpClient.get<SingleResponseModel<TenantCacheDetails>>(
      `${this.apiUrl}cacheowner/details`
    );
  }

  /**
   * Cache warmup işlemi başlatır
   */
  warmupCache(request: CacheWarmupRequest): Observable<SingleResponseModel<CacheWarmupResult>> {
    return this.httpClient.post<SingleResponseModel<CacheWarmupResult>>(
      `${this.apiUrl}cacheowner/warmup`, request
    );
  }

  /**
   * Belirli cache key'ini siler
   */
  deleteCacheKey(key: string): Observable<SingleResponseModel<{ key: string; removed: boolean }>> {
    return this.httpClient.delete<SingleResponseModel<{ key: string; removed: boolean }>>(
      `${this.apiUrl}cacheowner/keys/${encodeURIComponent(key)}`
    );
  }

  /**
   * Cache key'inin değerini getirir (debug amaçlı)
   */
  getCacheKeyValue(key: string): Observable<SingleResponseModel<CacheKeyValue>> {
    return this.httpClient.get<SingleResponseModel<CacheKeyValue>>(
      `${this.apiUrl}cacheowner/keys/${encodeURIComponent(key)}/value`
    );
  }

  /**
   * Real-time cache metrics getirir (Dashboard için)
   */
  getRealtimeMetrics(): Observable<SingleResponseModel<any>> {
    return this.httpClient.get<SingleResponseModel<any>>(
      `${this.apiUrl}cacheowner/metrics/realtime`
    );
  }

  // Multi-Company Cache Management Methods (Owner Only)

  /**
   * Tüm şirketlerin cache istatistiklerini getirir
   */
  getAllCompaniesStatistics(): Observable<SingleResponseModel<any>> {
    return this.httpClient.get<SingleResponseModel<any>>(
      `${this.apiUrl}cacheowner/companies/statistics`
    );
  }

  /**
   * Belirli bir şirketin cache detaylarını getirir
   */
  getSpecificCompanyCacheDetails(companyId: number): Observable<SingleResponseModel<any>> {
    return this.httpClient.get<SingleResponseModel<any>>(
      `${this.apiUrl}cacheowner/companies/${companyId}/details`
    );
  }

  /**
   * Belirli bir şirketin cache'ini temizler
   */
  clearSpecificCompanyCache(companyId: number): Observable<SingleResponseModel<any>> {
    return this.httpClient.delete<SingleResponseModel<any>>(
      `${this.apiUrl}cacheowner/companies/${companyId}/clear`
    );
  }

  /**
   * Çoklu şirket cache temizleme
   */
  bulkClearCompaniesCache(companyIds: number[]): Observable<SingleResponseModel<any>> {
    const request = {
      companyIds: companyIds,
      operation: 'clear'
    };
    return this.httpClient.post<SingleResponseModel<any>>(
      `${this.apiUrl}cacheowner/companies/bulk-clear`,
      request
    );
  }

  /**
   * Utility method'lar - Frontend cache yönetimi için
   */

  /**
   * Cache key'lerini entity'ye göre gruplar
   */
  groupKeysByEntity(keys: CacheKeyDetail[]): { [entity: string]: CacheKeyDetail[] } {
    return keys.reduce((groups, key) => {
      const parts = key.key.split(':');
      const entity = parts.length >= 3 ? parts[2] : 'unknown';

      if (!groups[entity]) {
        groups[entity] = [];
      }
      groups[entity].push(key);

      return groups;
    }, {} as { [entity: string]: CacheKeyDetail[] });
  }

  /**
   * Memory usage'ı human readable format'a çevirir
   */
  formatMemoryUsage(bytes: number): string {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * TTL'yi human readable format'a çevirir
   */
  formatTTL(seconds?: number): string {
    if (!seconds || seconds <= 0) return 'No expiry';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  }

  /**
   * Cache hit ratio'yu hesaplar
   */
  calculateHitRatio(hits: number, misses: number): number {
    const total = hits + misses;
    return total > 0 ? Math.round((hits / total) * 100) : 0;
  }

  /**
   * Predefined cache pattern'leri
   */
  getCachePatterns(): { [key: string]: string } {
    return {
      'All Members': 'member:*',
      'All Payments': 'payment:*',
      'All Memberships': 'membership:*',
      'All Users': 'user:*',
      'Company Settings': 'company:*',
      'Member Details': 'member:details',
      'Active Members': 'member:active',
      'Payment History': 'payment:history:*',
      'Membership Types': 'membershiptype:*'
    };
  }

  /**
   * Quick actions için predefined warmup configurations
   */
  getQuickWarmupConfigs(): { [key: string]: CacheWarmupRequest } {
    return {
      'Essential Data': {
        warmupMembers: true,
        warmupMemberships: true,
        warmupPayments: false,
        warmupUsers: false,
        warmupCompanySettings: true
      },
      'All Data': {
        warmupMembers: true,
        warmupMemberships: true,
        warmupPayments: true,
        warmupUsers: true,
        warmupCompanySettings: true
      },
      'Members Only': {
        warmupMembers: true,
        warmupMemberships: false,
        warmupPayments: false,
        warmupUsers: false,
        warmupCompanySettings: false
      }
    };
  }
}
