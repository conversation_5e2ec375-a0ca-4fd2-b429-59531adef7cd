using Business.Abstract;
using Entities.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace WebAPI.Controllers
{
    /// <summary>
    /// Cache Owner Controller - SOLID prensiplerine uygun
    /// Single Responsibility: Sadece HTTP request/response handling
    /// Open/Closed: Yeni cache endpoints için genişletilebilir
    /// Liskov Substitution: ControllerBase'i doğru implement eder
    /// Interface Segregation: Sadece cache owner operasyonları
    /// Dependency Inversion: ICacheOwnerService abstraction'ını kullanır
    /// 
    /// Sadece Owner yetkisi - Admin erişimi yok
    /// Multi-tenant cache isolation destekler
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class CacheOwnerController : ControllerBase
    {
        private readonly ICacheOwnerService _cacheOwnerService;

        public CacheOwnerController(ICacheOwnerService cacheOwnerService)
        {
            _cacheOwnerService = cacheOwnerService;
        }

        #region Company Cache Operations

        /// <summary>
        /// Cache istatistiklerini getirir (Company bazlı)
        /// </summary>
        /// <param name="companyId">Company ID (null ise context'ten alınır)</param>
        /// <returns>Cache istatistikleri</returns>
        [HttpGet("statistics")]
        public async Task<IActionResult> GetCacheStatistics([FromQuery] int? companyId = null)
        {
            var result = await _cacheOwnerService.GetCacheStatisticsAsync(companyId);
            
            if (result.Success)
                return Ok(result);
            
            return BadRequest(result);
        }

        /// <summary>
        /// Cache detaylarını getirir (Company bazlı)
        /// </summary>
        /// <param name="companyId">Company ID (null ise context'ten alınır)</param>
        /// <returns>Cache detayları</returns>
        [HttpGet("details")]
        public async Task<IActionResult> GetCacheDetails([FromQuery] int? companyId = null)
        {
            var result = await _cacheOwnerService.GetCacheDetailsAsync(companyId);
            
            if (result.Success)
                return Ok(result);
            
            return BadRequest(result);
        }

        /// <summary>
        /// Company cache'ini temizler
        /// </summary>
        /// <param name="companyId">Company ID (null ise context'ten alınır)</param>
        /// <returns>Temizleme sonucu</returns>
        [HttpDelete("clear")]
        public async Task<IActionResult> ClearCompanyCache([FromQuery] int? companyId = null)
        {
            var result = await _cacheOwnerService.ClearCompanyCacheAsync(companyId);
            
            if (result.Success)
                return Ok(result);
            
            return BadRequest(result);
        }

        #endregion

        #region Cache Key Operations

        /// <summary>
        /// Cache key'lerini sayfalı olarak getirir
        /// </summary>
        /// <param name="page">Sayfa numarası</param>
        /// <param name="size">Sayfa boyutu</param>
        /// <param name="companyId">Company ID (null ise context'ten alınır)</param>
        /// <returns>Cache key'leri</returns>
        [HttpGet("keys")]
        public async Task<IActionResult> GetCacheKeys([FromQuery] int page = 1, [FromQuery] int size = 50, [FromQuery] int? companyId = null)
        {
            var result = await _cacheOwnerService.GetCacheKeysAsync(page, size, companyId);
            
            if (result.Success)
                return Ok(result);
            
            return BadRequest(result);
        }

        /// <summary>
        /// Pattern'e göre cache key'lerini getirir
        /// </summary>
        /// <param name="pattern">Arama pattern'i</param>
        /// <param name="page">Sayfa numarası</param>
        /// <param name="size">Sayfa boyutu</param>
        /// <param name="companyId">Company ID (null ise context'ten alınır)</param>
        /// <returns>Pattern'e uyan cache key'leri</returns>
        [HttpGet("keys/pattern")]
        public async Task<IActionResult> GetCacheKeysByPattern([FromQuery] string pattern, [FromQuery] int page = 1, [FromQuery] int size = 50, [FromQuery] int? companyId = null)
        {
            var result = await _cacheOwnerService.GetCacheKeysByPatternAsync(pattern, page, size, companyId);
            
            if (result.Success)
                return Ok(result);
            
            return BadRequest(result);
        }

        /// <summary>
        /// Belirli bir cache key'ini siler
        /// </summary>
        /// <param name="key">Silinecek cache key</param>
        /// <returns>Silme sonucu</returns>
        [HttpDelete("keys/{key}")]
        public async Task<IActionResult> DeleteCacheKey(string key)
        {
            var result = await _cacheOwnerService.DeleteCacheKeyAsync(key);
            
            if (result.Success)
                return Ok(result);
            
            return BadRequest(result);
        }

        /// <summary>
        /// Cache key'inin değerini getirir
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Cache key değeri</returns>
        [HttpGet("keys/{key}/value")]
        public async Task<IActionResult> GetCacheKeyValue(string key)
        {
            var result = await _cacheOwnerService.GetCacheKeyValueAsync(key);
            
            if (result.Success)
                return Ok(result);
            
            return BadRequest(result);
        }

        #endregion

        #region Pattern Operations

        /// <summary>
        /// Pattern'e göre cache'i temizler
        /// </summary>
        /// <param name="pattern">Temizlenecek pattern</param>
        /// <param name="companyId">Company ID (null ise context'ten alınır)</param>
        /// <returns>Temizleme sonucu</returns>
        [HttpDelete("pattern")]
        public async Task<IActionResult> ClearCacheByPattern([FromQuery] string pattern, [FromQuery] int? companyId = null)
        {
            var result = await _cacheOwnerService.ClearCacheByPatternAsync(pattern, companyId);
            
            if (result.Success)
                return Ok(result);
            
            return BadRequest(result);
        }

        #endregion

        #region Health & Monitoring

        /// <summary>
        /// Cache sağlık durumunu kontrol eder
        /// </summary>
        /// <returns>Cache sağlık durumu</returns>
        [HttpGet("health")]
        public async Task<IActionResult> GetCacheHealth()
        {
            var result = await _cacheOwnerService.GetCacheHealthAsync();
            
            if (result.Success)
                return Ok(result);
            
            return BadRequest(result);
        }

        /// <summary>
        /// Real-time cache metrics getirir
        /// </summary>
        /// <param name="companyId">Company ID (null ise context'ten alınır)</param>
        /// <returns>Real-time cache metrics</returns>
        [HttpGet("metrics/realtime")]
        public async Task<IActionResult> GetRealtimeMetrics([FromQuery] int? companyId = null)
        {
            var result = await _cacheOwnerService.GetRealtimeMetricsAsync(companyId);
            
            if (result.Success)
                return Ok(result);
            
            return BadRequest(result);
        }

        /// <summary>
        /// Cache warmup işlemi başlatır
        /// </summary>
        /// <param name="request">Warmup parametreleri</param>
        /// <param name="companyId">Company ID (null ise context'ten alınır)</param>
        /// <returns>Warmup sonucu</returns>
        [HttpPost("warmup")]
        public async Task<IActionResult> PerformCacheWarmup([FromBody] CacheWarmupRequestDto request, [FromQuery] int? companyId = null)
        {
            var result = await _cacheOwnerService.PerformCacheWarmupAsync(request, companyId);
            
            if (result.Success)
                return Ok(result);
            
            return BadRequest(result);
        }

        #endregion

        #region Multi-Company Operations (Owner Only)

        /// <summary>
        /// Tüm şirketlerin cache istatistiklerini getirir (Sadece Owner)
        /// </summary>
        /// <returns>Çoklu şirket cache istatistikleri</returns>
        [HttpGet("companies/statistics")]
        public async Task<IActionResult> GetAllCompaniesStatistics()
        {
            var result = await _cacheOwnerService.GetAllCompaniesStatisticsAsync();

            if (result.Success)
                return Ok(result);

            return BadRequest(result);
        }

        /// <summary>
        /// Belirli bir şirketin cache detaylarını getirir (Sadece Owner)
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <returns>Şirket cache detayları</returns>
        [HttpGet("companies/{companyId}/details")]
        public async Task<IActionResult> GetSpecificCompanyCacheDetails(int companyId)
        {
            var result = await _cacheOwnerService.GetSpecificCompanyCacheDetailsAsync(companyId);

            if (result.Success)
                return Ok(result);

            return BadRequest(result);
        }

        /// <summary>
        /// Belirli bir şirketin cache'ini temizler (Sadece Owner)
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <returns>Temizleme sonucu</returns>
        [HttpDelete("companies/{companyId}/clear")]
        public async Task<IActionResult> ClearSpecificCompanyCache(int companyId)
        {
            var result = await _cacheOwnerService.ClearSpecificCompanyCacheAsync(companyId);

            if (result.Success)
                return Ok(result);

            return BadRequest(result);
        }

        /// <summary>
        /// Birden fazla şirketin cache'ini toplu olarak temizler (Sadece Owner)
        /// </summary>
        /// <param name="request">Toplu temizleme isteği</param>
        /// <returns>Toplu temizleme sonucu</returns>
        [HttpPost("companies/bulk-clear")]
        public async Task<IActionResult> BulkClearCompaniesCache([FromBody] BulkCacheOperationRequestDto request)
        {
            var result = await _cacheOwnerService.BulkClearCompaniesCache(request);

            if (result.Success)
                return Ok(result);

            return BadRequest(result);
        }

        #endregion
    }
}
