import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, interval, Subscription } from 'rxjs';
import { CacheAdminService } from './cache-admin.service';
import { ToastrService } from 'ngx-toastr';

export interface CacheAlert {
  id: string;
  type: 'warning' | 'error' | 'info' | 'success';
  title: string;
  message: string;
  companyId?: number;
  companyName?: string;
  timestamp: Date;
  isRead: boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
  data?: any;
}

export interface AlertThresholds {
  memoryUsageWarning: number; // MB
  memoryUsageCritical: number; // MB
  keyCountWarning: number;
  keyCountCritical: number;
  responseTimeWarning: number; // ms
  responseTimeCritical: number; // ms
  lowTTLWarning: number; // seconds
}

@Injectable({
  providedIn: 'root'
})
export class CacheAlertService {
  private alertsSubject = new BehaviorSubject<CacheAlert[]>([]);
  private monitoringSubscription: Subscription | null = null;
  private isMonitoring = false;
  
  // Alert thresholds
  private thresholds: AlertThresholds = {
    memoryUsageWarning: 50, // 50MB
    memoryUsageCritical: 100, // 100MB
    keyCountWarning: 5000,
    keyCountCritical: 10000,
    responseTimeWarning: 100, // 100ms
    responseTimeCritical: 500, // 500ms
    lowTTLWarning: 300 // 5 minutes
  };

  // Monitoring settings
  private monitoringInterval = 30000; // 30 seconds
  private maxAlerts = 100; // Maximum alerts to keep in memory

  constructor(
    private cacheAdminService: CacheAdminService,
    private toastr: ToastrService
  ) {
    this.loadAlertsFromStorage();
  }

  /**
   * Get alerts observable
   */
  get alerts$(): Observable<CacheAlert[]> {
    return this.alertsSubject.asObservable();
  }

  /**
   * Get current alerts
   */
  get alerts(): CacheAlert[] {
    return this.alertsSubject.value;
  }

  /**
   * Start monitoring
   */
  startMonitoring(): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.monitoringSubscription = interval(this.monitoringInterval).subscribe(() => {
      this.checkCacheHealth();
    });

    this.addAlert({
      type: 'info',
      title: 'Monitoring Başlatıldı',
      message: 'Cache monitoring sistemi aktif edildi',
      severity: 'low'
    });
  }

  /**
   * Stop monitoring
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;
    if (this.monitoringSubscription) {
      this.monitoringSubscription.unsubscribe();
      this.monitoringSubscription = null;
    }

    this.addAlert({
      type: 'info',
      title: 'Monitoring Durduruldu',
      message: 'Cache monitoring sistemi deaktif edildi',
      severity: 'low'
    });
  }

  /**
   * Check cache health for all companies
   */
  private async checkCacheHealth(): Promise<void> {
    try {
      // Single company check (current user's company)
      const response = await this.cacheAdminService.getRealtimeMetrics().toPromise();
      if (response?.success) {
        this.analyzeMetrics(response.data);
      }

      // Multi-company check (if user has access)
      try {
        const multiResponse = await this.cacheAdminService.getAllCompaniesStatistics().toPromise();
        if (multiResponse?.success) {
          this.analyzeMultiCompanyMetrics(multiResponse.data);
        }
      } catch (error) {
        // User might not have multi-company access, ignore
      }
    } catch (error) {
      this.addAlert({
        type: 'error',
        title: 'Monitoring Hatası',
        message: 'Cache health check sırasında hata oluştu',
        severity: 'medium',
        data: { error: error }
      });
    }
  }

  /**
   * Analyze single company metrics
   */
  private analyzeMetrics(metrics: any): void {
    const companyId = metrics.companyId;
    const stats = metrics.statistics;
    const health = metrics.health;

    // Memory usage check
    if (stats.totalMemoryUsageMB > this.thresholds.memoryUsageCritical) {
      this.addAlert({
        type: 'error',
        title: 'Kritik Memory Kullanımı',
        message: `Memory kullanımı ${stats.totalMemoryUsageMB.toFixed(2)}MB (Limit: ${this.thresholds.memoryUsageCritical}MB)`,
        companyId: companyId,
        severity: 'critical',
        data: { memoryUsage: stats.totalMemoryUsageMB }
      });
    } else if (stats.totalMemoryUsageMB > this.thresholds.memoryUsageWarning) {
      this.addAlert({
        type: 'warning',
        title: 'Yüksek Memory Kullanımı',
        message: `Memory kullanımı ${stats.totalMemoryUsageMB.toFixed(2)}MB (Uyarı: ${this.thresholds.memoryUsageWarning}MB)`,
        companyId: companyId,
        severity: 'medium',
        data: { memoryUsage: stats.totalMemoryUsageMB }
      });
    }

    // Key count check
    if (stats.totalKeys > this.thresholds.keyCountCritical) {
      this.addAlert({
        type: 'error',
        title: 'Kritik Cache Key Sayısı',
        message: `Cache key sayısı ${stats.totalKeys} (Limit: ${this.thresholds.keyCountCritical})`,
        companyId: companyId,
        severity: 'critical',
        data: { keyCount: stats.totalKeys }
      });
    } else if (stats.totalKeys > this.thresholds.keyCountWarning) {
      this.addAlert({
        type: 'warning',
        title: 'Yüksek Cache Key Sayısı',
        message: `Cache key sayısı ${stats.totalKeys} (Uyarı: ${this.thresholds.keyCountWarning})`,
        companyId: companyId,
        severity: 'medium',
        data: { keyCount: stats.totalKeys }
      });
    }

    // Response time check
    if (health.responseTime > this.thresholds.responseTimeCritical) {
      this.addAlert({
        type: 'error',
        title: 'Kritik Response Time',
        message: `Cache response time ${health.responseTime}ms (Limit: ${this.thresholds.responseTimeCritical}ms)`,
        companyId: companyId,
        severity: 'critical',
        data: { responseTime: health.responseTime }
      });
    } else if (health.responseTime > this.thresholds.responseTimeWarning) {
      this.addAlert({
        type: 'warning',
        title: 'Yavaş Response Time',
        message: `Cache response time ${health.responseTime}ms (Uyarı: ${this.thresholds.responseTimeWarning}ms)`,
        companyId: companyId,
        severity: 'medium',
        data: { responseTime: health.responseTime }
      });
    }

    // Connection check
    if (!health.isConnected) {
      this.addAlert({
        type: 'error',
        title: 'Cache Bağlantı Hatası',
        message: 'Redis cache sunucusuna bağlantı kurulamıyor',
        companyId: companyId,
        severity: 'critical',
        data: { connectionError: true }
      });
    }
  }

  /**
   * Analyze multi-company metrics
   */
  private analyzeMultiCompanyMetrics(data: any): void {
    const companies = data.companies || [];
    
    companies.forEach((company: any) => {
      const stats = company.statistics;
      
      // Check each company's metrics
      if (stats.totalMemoryUsageMB > this.thresholds.memoryUsageCritical) {
        this.addAlert({
          type: 'error',
          title: 'Şirket Cache Kritik',
          message: `${company.companyName} - Memory: ${stats.totalMemoryUsageMB.toFixed(2)}MB`,
          companyId: company.companyId,
          companyName: company.companyName,
          severity: 'critical',
          data: { memoryUsage: stats.totalMemoryUsageMB }
        });
      }
    });

    // Global checks
    if (data.totalMemoryUsage > (this.thresholds.memoryUsageCritical * companies.length * 0.8)) {
      this.addAlert({
        type: 'warning',
        title: 'Global Memory Uyarısı',
        message: `Toplam memory kullanımı yüksek: ${this.formatMemory(data.totalMemoryUsage)}`,
        severity: 'high',
        data: { globalMemoryUsage: data.totalMemoryUsage }
      });
    }
  }

  /**
   * Add new alert
   */
  private addAlert(alertData: Partial<CacheAlert>): void {
    const alert: CacheAlert = {
      id: this.generateAlertId(),
      type: alertData.type || 'info',
      title: alertData.title || 'Cache Alert',
      message: alertData.message || '',
      companyId: alertData.companyId,
      companyName: alertData.companyName,
      timestamp: new Date(),
      isRead: false,
      severity: alertData.severity || 'low',
      data: alertData.data
    };

    // Check for duplicate alerts (same type, company, and similar message within last 5 minutes)
    const isDuplicate = this.alerts.some(existingAlert => 
      existingAlert.type === alert.type &&
      existingAlert.companyId === alert.companyId &&
      existingAlert.title === alert.title &&
      (new Date().getTime() - existingAlert.timestamp.getTime()) < 300000 // 5 minutes
    );

    if (isDuplicate) return;

    const currentAlerts = this.alerts;
    currentAlerts.unshift(alert);

    // Keep only max alerts
    if (currentAlerts.length > this.maxAlerts) {
      currentAlerts.splice(this.maxAlerts);
    }

    this.alertsSubject.next(currentAlerts);
    this.saveAlertsToStorage();

    // Show toastr notification for high/critical alerts
    if (alert.severity === 'high' || alert.severity === 'critical') {
      if (alert.type === 'error') {
        this.toastr.error(alert.message, alert.title, { timeOut: 10000 });
      } else if (alert.type === 'warning') {
        this.toastr.warning(alert.message, alert.title, { timeOut: 8000 });
      }
    }
  }

  /**
   * Mark alert as read
   */
  markAsRead(alertId: string): void {
    const alerts = this.alerts.map(alert => 
      alert.id === alertId ? { ...alert, isRead: true } : alert
    );
    this.alertsSubject.next(alerts);
    this.saveAlertsToStorage();
  }

  /**
   * Mark all alerts as read
   */
  markAllAsRead(): void {
    const alerts = this.alerts.map(alert => ({ ...alert, isRead: true }));
    this.alertsSubject.next(alerts);
    this.saveAlertsToStorage();
  }

  /**
   * Delete specific alert
   */
  deleteAlert(alertId: string): void {
    const currentAlerts = this.alertsSubject.value;
    const updatedAlerts = currentAlerts.filter(alert => alert.id !== alertId);
    this.alertsSubject.next(updatedAlerts);
    this.saveAlertsToStorage();
  }

  /**
   * Clear all alerts
   */
  clearAllAlerts(): void {
    this.alertsSubject.next([]);
    this.saveAlertsToStorage();
  }

  /**
   * Get unread alerts count
   */
  getUnreadCount(): number {
    return this.alerts.filter(alert => !alert.isRead).length;
  }

  /**
   * Update thresholds
   */
  updateThresholds(newThresholds: Partial<AlertThresholds>): void {
    this.thresholds = { ...this.thresholds, ...newThresholds };
    localStorage.setItem('cache-alert-thresholds', JSON.stringify(this.thresholds));
  }

  /**
   * Get current thresholds
   */
  getThresholds(): AlertThresholds {
    return { ...this.thresholds };
  }

  /**
   * Private helper methods
   */
  private generateAlertId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private formatMemory(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  private saveAlertsToStorage(): void {
    try {
      localStorage.setItem('cache-alerts', JSON.stringify(this.alerts));
    } catch (error) {
      console.warn('Failed to save alerts to localStorage:', error);
    }
  }

  private loadAlertsFromStorage(): void {
    try {
      const stored = localStorage.getItem('cache-alerts');
      if (stored) {
        const alerts = JSON.parse(stored).map((alert: any) => ({
          ...alert,
          timestamp: new Date(alert.timestamp)
        }));
        this.alertsSubject.next(alerts);
      }

      const storedThresholds = localStorage.getItem('cache-alert-thresholds');
      if (storedThresholds) {
        this.thresholds = { ...this.thresholds, ...JSON.parse(storedThresholds) };
      }
    } catch (error) {
      console.warn('Failed to load alerts from localStorage:', error);
    }
  }

  /**
   * Get monitoring status
   */
  isMonitoringActive(): boolean {
    return this.isMonitoring;
  }
}
