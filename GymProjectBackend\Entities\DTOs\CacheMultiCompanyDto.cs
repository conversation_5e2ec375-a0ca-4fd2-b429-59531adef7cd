using System;
using System.Collections.Generic;

namespace Entities.DTOs
{
    /// <summary>
    /// Çoklu şirket cache istatistikleri DTO
    /// </summary>
    public class MultiCompanyCacheStatisticsDto
    {
        public int TotalCompanies { get; set; }
        public long TotalCacheKeys { get; set; }
        public long TotalMemoryUsage { get; set; }
        public double AverageKeysPerCompany { get; set; }
        public List<CompanyCacheStatisticsDto> Companies { get; set; } = new List<CompanyCacheStatisticsDto>();
    }

    /// <summary>
    /// Şirket cache istatistikleri DTO
    /// </summary>
    public class CompanyCacheStatisticsDto
    {
        public int CompanyId { get; set; }
        public string CompanyName { get; set; }
        public CacheStatisticsDto Statistics { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreationDate { get; set; }
    }

    /// <summary>
    /// Şirket cache detayları DTO
    /// </summary>
    public class CompanyCacheDetailsDto
    {
        public CompanyDetailDto Company { get; set; }
        public CacheDetailsDto CacheDetails { get; set; }
    }

    /// <summary>
    /// Bulk cache operation request DTO
    /// </summary>
    public class BulkCacheOperationRequestDto
    {
        public List<int> CompanyIds { get; set; } = new List<int>();
        public string Operation { get; set; } // "clear", "warmup", etc.
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Bulk cache operation sonucu DTO
    /// </summary>
    public class BulkCacheOperationResultDto
    {
        public long TotalRemovedCount { get; set; }
        public int ProcessedCompanies { get; set; }
        public List<BulkCacheOperationItemResultDto> Results { get; set; } = new List<BulkCacheOperationItemResultDto>();
    }

    /// <summary>
    /// Bulk cache operation item sonucu DTO
    /// </summary>
    public class BulkCacheOperationItemResultDto
    {
        public int CompanyId { get; set; }
        public string CompanyName { get; set; }
        public bool Success { get; set; }
        public long RemovedCount { get; set; }
        public string Message { get; set; }
    }
}
