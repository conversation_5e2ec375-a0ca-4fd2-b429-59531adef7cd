using Core.Utilities.Results;
using Entities.DTOs;
using System.Threading.Tasks;

namespace Business.Abstract
{
    /// <summary>
    /// Cache Owner Service Interface - SOLID prensiplerine uygun
    /// Single Responsibility: Sadece cache owner operasyonları
    /// Interface Segregation: <PERSON><PERSON>e gerekli cache owner metodları
    /// Dependency Inversion: Abstraction tanımı
    /// </summary>
    public interface ICacheOwnerService
    {
        #region Company Cache Operations

        /// <summary>
        /// Company'ye ait cache istatistiklerini getirir
        /// </summary>
        /// <param name="companyId">Company ID (null ise context'ten alınır)</param>
        /// <returns>Cache istatistikleri</returns>
        Task<IDataResult<CacheStatisticsDto>> GetCacheStatisticsAsync(int? companyId = null);

        /// <summary>
        /// Company'ye ait detaylı cache bilgilerini getirir
        /// </summary>
        /// <param name="companyId">Company ID (null ise context'ten alınır)</param>
        /// <returns>Cache detayları</returns>
        Task<IDataResult<CacheDetailsDto>> GetCacheDetailsAsync(int? companyId = null);

        /// <summary>
        /// Company'nin tüm cache'ini temizler
        /// </summary>
        /// <param name="companyId">Company ID (null ise context'ten alınır)</param>
        /// <returns>Temizleme sonucu</returns>
        Task<IDataResult<CacheClearResultDto>> ClearCompanyCacheAsync(int? companyId = null);

        #endregion

        #region Cache Key Operations

        /// <summary>
        /// Company'ye ait cache key'lerini sayfalı olarak getirir
        /// </summary>
        /// <param name="page">Sayfa numarası</param>
        /// <param name="size">Sayfa boyutu</param>
        /// <param name="companyId">Company ID (null ise context'ten alınır)</param>
        /// <returns>Cache key'leri</returns>
        Task<IDataResult<CacheKeysResponseDto>> GetCacheKeysAsync(int page = 1, int size = 50, int? companyId = null);

        /// <summary>
        /// Belirli pattern'e göre cache key'lerini getirir
        /// </summary>
        /// <param name="pattern">Arama pattern'i</param>
        /// <param name="page">Sayfa numarası</param>
        /// <param name="size">Sayfa boyutu</param>
        /// <param name="companyId">Company ID (null ise context'ten alınır)</param>
        /// <returns>Pattern'e uyan cache key'leri</returns>
        Task<IDataResult<CacheKeysResponseDto>> GetCacheKeysByPatternAsync(string pattern, int page = 1, int size = 50, int? companyId = null);

        /// <summary>
        /// Belirli cache key'ini siler
        /// </summary>
        /// <param name="key">Silinecek cache key</param>
        /// <returns>Silme sonucu</returns>
        Task<IDataResult<CacheKeyDeleteResultDto>> DeleteCacheKeyAsync(string key);

        /// <summary>
        /// Cache key'inin değerini getirir
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Key değeri</returns>
        Task<IDataResult<CacheKeyValueDto>> GetCacheKeyValueAsync(string key);

        /// <summary>
        /// Belirli pattern'e göre cache key'lerini listeler
        /// </summary>
        /// <param name="pattern">Aranacak pattern</param>
        /// <param name="page">Sayfa numarası</param>
        /// <param name="size">Sayfa boyutu</param>
        /// <param name="companyId">Company ID (null ise context'ten alınır)</param>
        /// <returns>Pattern'e uyan cache key'leri</returns>
        Task<IDataResult<CacheKeysResponseDto>> GetKeysByPatternAsync(string pattern, int page = 1, int size = 50, int? companyId = null);

        #endregion

        #region Pattern Operations

        /// <summary>
        /// Belirli pattern'deki cache'leri temizler
        /// </summary>
        /// <param name="pattern">Temizlenecek pattern</param>
        /// <param name="companyId">Company ID (null ise context'ten alınır)</param>
        /// <returns>Temizleme sonucu</returns>
        Task<IDataResult<CacheClearResultDto>> ClearCacheByPatternAsync(string pattern, int? companyId = null);

        #endregion

        #region Health & Monitoring

        /// <summary>
        /// Cache sağlık durumunu kontrol eder
        /// </summary>
        /// <returns>Cache sağlık bilgileri</returns>
        Task<IDataResult<CacheHealthDto>> GetCacheHealthAsync();

        /// <summary>
        /// Real-time cache metrics getirir
        /// </summary>
        /// <param name="companyId">Company ID (null ise context'ten alınır)</param>
        /// <returns>Real-time metrics</returns>
        Task<IDataResult<CacheRealtimeMetricsDto>> GetRealtimeMetricsAsync(int? companyId = null);

        /// <summary>
        /// Cache warmup işlemi başlatır
        /// </summary>
        /// <param name="request">Warmup parametreleri</param>
        /// <param name="companyId">Company ID (null ise context'ten alınır)</param>
        /// <returns>Warmup sonucu</returns>
        Task<IDataResult<CacheWarmupResultDto>> PerformCacheWarmupAsync(CacheWarmupRequestDto request, int? companyId = null);

        #endregion

        #region Multi-Company Operations (Owner Only)

        /// <summary>
        /// Tüm şirketlerin cache istatistiklerini getirir
        /// </summary>
        /// <returns>Çoklu şirket cache istatistikleri</returns>
        Task<IDataResult<MultiCompanyCacheStatisticsDto>> GetAllCompaniesStatisticsAsync();

        /// <summary>
        /// Belirli şirketin cache detaylarını getirir
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <returns>Şirket cache detayları</returns>
        Task<IDataResult<CompanyCacheDetailsDto>> GetSpecificCompanyCacheDetailsAsync(int companyId);

        /// <summary>
        /// Belirli şirketin cache'ini temizler
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <returns>Temizleme sonucu</returns>
        Task<IDataResult<CacheClearResultDto>> ClearSpecificCompanyCacheAsync(int companyId);

        /// <summary>
        /// Çoklu şirket cache temizleme işlemi
        /// </summary>
        /// <param name="request">Bulk operation parametreleri</param>
        /// <returns>Bulk temizleme sonucu</returns>
        Task<IDataResult<BulkCacheOperationResultDto>> BulkClearCompaniesCache(BulkCacheOperationRequestDto request);

        /// <summary>
        /// Real-time cache metrics getirir
        /// </summary>
        /// <returns>Real-time metrics</returns>
        Task<IDataResult<object>> GetRealtimeMetricsAsync();

        #endregion
    }
}
