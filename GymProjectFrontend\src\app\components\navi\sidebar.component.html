<div
  class="sidebar"
  [ngClass]="{ collapsed: collapsed, hovered: isHovered }"
  (mouseenter)="onSidebarMouseEnter()"
  (mouseleave)="onSidebarMouseLeave()"
>
  <!-- Sidebar Header -->
  <div class="sidebar-header">
    <a routerLink="/todayentries" class="logo-link">
      <!-- Yönlendirme eklendi -->
      <div class="logo-container">
        <i class="fas fa-dumbbell"></i>
        <span class="logo-text" *ngIf="!collapsed || isHovered"
          >GymKod Pro</span
        >
      </div>
    </a>
    <button
      class="toggle-btn"
      (click)="onToggleSidebar()"
      title="Kenar Çubuğunu Daralt/Genişlet"
    >
      <i
        class="fas"
        [ngClass]="collapsed ? 'fa-angle-right' : 'fa-angle-left'"
      ></i>
    </button>
  </div>

  <!-- Sidebar Content -->
  <div class="sidebar-content">
    <!-- Member QR Section -->
    <div
      class="menu-section"
      *ngIf="isAuthenticated() && isMember && hasMemberQRAccess"
    >
      <a class="menu-item" routerLink="my-qr" routerLinkActive="active">
        <i class="fas fa-qrcode"></i>
        <span *ngIf="!collapsed || isHovered">QR Kodum</span>
      </a>
    </div>

    <!-- Customer Section -->
    <div class="menu-section" *ngIf="isAuthenticated() && isAdmin">
      <div
        class="menu-header"
        (click)="toggleMenuSection('customer')"
        [attr.data-title]="collapsed && !isHovered ? 'Müşteri' : null"
      >
        <div class="menu-icon">
          <i class="fas fa-users"></i>
        </div>
        <span class="menu-title" *ngIf="!collapsed || isHovered">Müşteri</span>
        <i
          class="fas fa-chevron-down menu-arrow"
          *ngIf="!collapsed || isHovered"
          [ngClass]="{ rotated: customerMenuOpen }"
        ></i>
      </div>

      <div
        class="menu-items"
        [ngClass]="{ expanded: customerMenuOpen || collapsed }"
      >
        <a
          class="menu-item"
          routerLink="allmembers"
          routerLinkActive="active"
          [attr.data-title]="collapsed && !isHovered ? 'Bütün Üyeler' : null"
        >
          <i class="fas fa-list"></i>
          <span *ngIf="!collapsed || isHovered">Bütün Üyeler</span>
        </a>
        <a
          class="menu-item"
          routerLink="memberfilter"
          routerLinkActive="active"
          [attr.data-title]="collapsed && !isHovered ? 'Aktif Üyeler' : null"
        >
          <i class="fas fa-user-check"></i>
          <span *ngIf="!collapsed || isHovered">Aktif Üyeler</span>
        </a>
        <a
          class="menu-item"
          routerLink="frozen-memberships"
          routerLinkActive="active"
        >
          <i class="fas fa-snowflake"></i>
          <span *ngIf="!collapsed || isHovered">Dondurulmuş Üyelikler</span>
        </a>
        <a
          class="menu-item"
          routerLink="memberremainingday"
          routerLinkActive="active"
        >
          <i class="fas fa-hourglass-end"></i>
          <span *ngIf="!collapsed || isHovered">Üyelik Bitişi Yaklaşanlar</span>
        </a>
        <a
          class="menu-item"
          routerLink="debtormember"
          routerLinkActive="active"
        >
          <i class="fas fa-hand-holding-usd"></i>
          <span *ngIf="!collapsed || isHovered">Borçlu Üyeler</span>
        </a>
        <a class="menu-item" routerLink="birthdays" routerLinkActive="active">
          <i class="fas fa-birthday-cake"></i>
          <span *ngIf="!collapsed || isHovered">Doğum Günleri</span>
        </a>
        <a
          class="menu-item"
          routerLink="todayentries"
          routerLinkActive="active"
        >
          <i class="fas fa-door-open"></i>
          <span *ngIf="!collapsed || isHovered">Giriş-Çıkış Kayıtları</span>
        </a>
        <a
          class="menu-item"
          routerLink="paymenthistory"
          routerLinkActive="active"
        >
          <i class="fas fa-cash-register"></i>
          <span *ngIf="!collapsed || isHovered">Kasa Raporu</span>
        </a>
        <a class="menu-item" routerLink="/expenses" routerLinkActive="active">
          <!-- Gider Yönetimi Eklendi -->
          <i class="fas fa-file-invoice-dollar"></i>
          <span *ngIf="!collapsed || isHovered">Gider Yönetimi</span>
        </a>
        <a
          class="menu-item"
          routerLink="membershiptype/add"
          routerLinkActive="active"
        >
          <i class="fas fa-plus-circle"></i>
          <span *ngIf="!collapsed || isHovered">Üyelik Türü Ekleme</span>
        </a>
        <a class="menu-item" routerLink="member/add" routerLinkActive="active">
          <i class="fas fa-user-plus"></i>
          <span *ngIf="!collapsed || isHovered">Yeni Üye</span>
        </a>
        <a
          class="menu-item"
          routerLink="membership/add"
          routerLinkActive="active"
        >
          <i class="fas fa-clock"></i>
          <span *ngIf="!collapsed || isHovered">Üyelik Süresi Ekleme</span>
        </a>
      </div>
    </div>

    <!-- E-Para Section -->
    <div class="menu-section" *ngIf="isAuthenticated() && isAdmin">
      <div class="menu-header" (click)="toggleMenuSection('eMoney')">
        <div class="menu-icon">
          <i class="fas fa-wallet"></i>
        </div>
        <span class="menu-title" *ngIf="!collapsed || isHovered">E-Para</span>
        <i
          class="fas fa-chevron-down menu-arrow"
          *ngIf="!collapsed || isHovered"
          [ngClass]="{ rotated: eMoneyMenuOpen }"
        ></i>
      </div>

      <div
        class="menu-items"
        [ngClass]="{ expanded: eMoneyMenuOpen || collapsed }"
      >
        <a
          class="menu-item"
          routerLink="memberbalancetopup"
          routerLinkActive="active"
        >
          <i class="fas fa-money-bill-wave"></i>
          <span *ngIf="!collapsed || isHovered">Bakiye Yükle - Düşür</span>
        </a>
        <a class="menu-item" routerLink="products" routerLinkActive="active">
          <i class="fas fa-shopping-basket"></i>
          <span *ngIf="!collapsed || isHovered">Ürün Ekle</span>
        </a>
        <a
          class="menu-item"
          routerLink="product-sale"
          routerLinkActive="active"
        >
          <i class="fas fa-shopping-cart"></i>
          <span *ngIf="!collapsed || isHovered">Ürün Sat</span>
        </a>
        <a
          class="menu-item"
          routerLink="transactions"
          routerLinkActive="active"
        >
          <i class="fas fa-exchange-alt"></i>
          <span *ngIf="!collapsed || isHovered">İşlem Takibi</span>
        </a>
      </div>
    </div>

    <!-- Training Section -->
    <div class="menu-section" *ngIf="isAuthenticated() && isAdmin">
      <div class="menu-header" (click)="toggleMenuSection('training')">
        <div class="menu-icon">
          <i class="fas fa-dumbbell"></i>
        </div>
        <span class="menu-title" *ngIf="!collapsed || isHovered"
          >Antrenman</span
        >
        <i
          class="fas fa-chevron-down menu-arrow"
          *ngIf="!collapsed || isHovered"
          [ngClass]="{ rotated: trainingMenuOpen }"
        ></i>
      </div>

      <div
        class="menu-items"
        [ngClass]="{ expanded: trainingMenuOpen || collapsed }"
      >
        <a class="menu-item" routerLink="/exercises" routerLinkActive="active">
          <i class="fas fa-list-ul"></i>
          <span *ngIf="!collapsed || isHovered">Egzersiz Listesi</span>
        </a>
        <a
          class="menu-item"
          routerLink="/workout-programs"
          routerLinkActive="active"
        >
          <i class="fas fa-dumbbell"></i>
          <span *ngIf="!collapsed || isHovered">Antrenman Programları</span>
        </a>
        <a
          class="menu-item"
          routerLink="/member-workout-assignments"
          routerLinkActive="active"
        >
          <i class="fas fa-user-plus"></i>
          <span *ngIf="!collapsed || isHovered">Program Atamaları</span>
        </a>
        <!-- Gelecekte eklenecek antrenman modülleri için yer -->
        <!--
        <a class="menu-item" routerLink="/workout-templates" routerLinkActive="active">
          <i class="fas fa-file-alt"></i>
          <span *ngIf="!collapsed || isHovered">Program Şablonları</span>
        </a>
        -->
      </div>
    </div>

    <!-- Owner Dashboard Section -->
    <div class="menu-section" *ngIf="isOwner">
      <a
        class="menu-item single-item"
        routerLink="license-dashboard"
        routerLinkActive="active"
      >
        <i class="fas fa-tachometer-alt"></i>
        <span *ngIf="!collapsed || isHovered">Ana Panel</span>
      </a>
    </div>

    <!-- Gym Management Section -->
    <div class="menu-section" *ngIf="isOwner">
      <div class="menu-header" (click)="toggleMenuSection('gym')">
        <div class="menu-icon">
          <i class="fas fa-building"></i>
        </div>
        <span class="menu-title" *ngIf="!collapsed || isHovered"
          >Salon Yönetimi</span
        >
        <i
          class="fas fa-chevron-down menu-arrow"
          *ngIf="!collapsed || isHovered"
          [ngClass]="{ rotated: gymMenuOpen }"
        ></i>
      </div>

      <div
        class="menu-items"
        [ngClass]="{ expanded: gymMenuOpen || collapsed }"
      >
        <a
          class="menu-item"
          routerLink="company/unified-add"
          routerLinkActive="active"
        >
          <i class="fas fa-plus-square"></i>
          <span *ngIf="!collapsed || isHovered">Yeni Salon Ekle</span>
        </a>
        <a
          class="menu-item"
          routerLink="companyuserdetails"
          routerLinkActive="active"
        >
          <i class="fas fa-id-card"></i>
          <span *ngIf="!collapsed || isHovered">Salon Sahipleri</span>
        </a>
        <a
          class="menu-item"
          routerLink="license-transactions"
          routerLinkActive="active"
        >
          <i class="fas fa-receipt"></i>
          <span *ngIf="!collapsed || isHovered">Satış Raporları</span>
        </a>
        <a
          class="menu-item"
          routerLink="deleted-companies"
          routerLinkActive="active"
        >
          <i class="fas fa-trash-restore"></i>
          <span *ngIf="!collapsed || isHovered">Pasif Salonlar</span>
        </a>
      </div>
    </div>

    <!-- License Management Section -->
    <div class="menu-section" *ngIf="isOwner">
      <div class="menu-header" (click)="toggleMenuSection('license')">
        <div class="menu-icon">
          <i class="fas fa-certificate"></i>
        </div>
        <span class="menu-title" *ngIf="!collapsed || isHovered"
          >Lisans Yönetimi</span
        >
        <i
          class="fas fa-chevron-down menu-arrow"
          *ngIf="!collapsed || isHovered"
          [ngClass]="{ rotated: licenseMenuOpen }"
        ></i>
      </div>

      <div
        class="menu-items"
        [ngClass]="{ expanded: licenseMenuOpen || collapsed }"
      >
        <a
          class="menu-item"
          routerLink="license-packages-add"
          routerLinkActive="active"
        >
          <i class="fas fa-box-open"></i>
          <span *ngIf="!collapsed || isHovered">Lisans Paketleri</span>
        </a>
        <a
          class="menu-item"
          routerLink="user-licenses"
          routerLinkActive="active"
        >
          <i class="fas fa-user-tag"></i>
          <span *ngIf="!collapsed || isHovered">Aktif Lisanslar</span>
        </a>
        <a
          class="menu-item"
          routerLink="expired-licenses"
          routerLinkActive="active"
        >
          <i class="fas fa-exclamation-triangle"></i>
          <span *ngIf="!collapsed || isHovered">Lisansı Dolan Üyeler</span>
        </a>
      </div>
    </div>

    <!-- System Management Section -->
    <div class="menu-section" *ngIf="isOwner">
      <div class="menu-header" (click)="toggleMenuSection('system')">
        <div class="menu-icon">
          <i class="fas fa-cogs"></i>
        </div>
        <span class="menu-title" *ngIf="!collapsed || isHovered"
          >Sistem Yönetimi</span
        >
        <i
          class="fas fa-chevron-down menu-arrow"
          *ngIf="!collapsed || isHovered"
          [ngClass]="{ rotated: systemMenuOpen }"
        ></i>
      </div>

      <div
        class="menu-items"
        [ngClass]="{ expanded: systemMenuOpen || collapsed }"
      >
        <a class="menu-item" routerLink="roles" routerLinkActive="active">
          <i class="fas fa-user-shield"></i>
          <span *ngIf="!collapsed || isHovered">Rol Yönetimi</span>
        </a>
        <a class="menu-item" routerLink="user-roles" routerLinkActive="active">
          <i class="fas fa-users-cog"></i>
          <span *ngIf="!collapsed || isHovered">Kullanıcı Rolleri</span>
        </a>
        <a class="menu-item" routerLink="devices" routerLinkActive="active">
          <i class="fas fa-mobile-alt"></i>
          <span *ngIf="!collapsed || isHovered">Aktif Cihazlar</span>
        </a>
        <a class="menu-item" routerLink="cache-dashboard" routerLinkActive="active">
          <i class="fas fa-tachometer-alt"></i>
          <span *ngIf="!collapsed || isHovered">Cache Dashboard</span>
        </a>
        <a
          class="menu-item"
          routerLink="rate-limit-test"
          routerLinkActive="active"
        >
          <i class="fas fa-shield-alt"></i>
          <span *ngIf="!collapsed || isHovered">Güvenlik Testi</span>
        </a>
        <a
          class="menu-item"
          routerLink="company-selector"
          routerLinkActive="active"
        >
          <i class="fas fa-exchange-alt"></i>
          <span *ngIf="!collapsed || isHovered">Salon Değiştir</span>
        </a>
      </div>
    </div>
  </div>

  <!-- Sidebar Footer -->
  <div class="sidebar-footer">
    <div class="user-profile-container" *ngIf="isAuthenticated()">
      <a class="profile-btn" routerLink="/profile">
        <!-- Profile Image or Icon -->
        <div class="profile-image-container">
          <!-- Profil fotoğrafı varsa göster -->
          <img
            *ngIf="profileImageState.hasImage && profileImageState.imageUrl"
            [src]="profileImageState.imageUrl"
            alt="Profil Fotoğrafı"
            class="profile-image"
            loading="lazy"
            decoding="async"
            (error)="onProfileImageError()"
          />

          <!-- Loading durumu -->
          <div *ngIf="profileImageState.isLoading" class="profile-loading">
            <i class="fas fa-spinner fa-spin"></i>
          </div>

          <!-- Varsayılan ikon (fotoğraf yoksa) -->
          <i
            *ngIf="!profileImageState.hasImage && !profileImageState.isLoading"
            class="fas fa-user-circle profile-icon"
          ></i>
        </div>
        <span *ngIf="!collapsed || isHovered">Profilim</span>
      </a>
    </div>

    <div class="theme-toggle-container">
      <button
        class="theme-toggle-btn"
        (click)="onToggleDarkMode()"
        *ngIf="!collapsed || isHovered"
      >
        <i class="fas" [ngClass]="isDarkMode ? 'fa-sun' : 'fa-moon'"></i>
        <span>{{ isDarkMode ? "Aydınlık Mod" : "Karanlık Mod" }}</span>
      </button>
      <button
        class="theme-toggle-btn icon-only"
        (click)="onToggleDarkMode()"
        *ngIf="collapsed && !isHovered"
      >
        <i class="fas" [ngClass]="isDarkMode ? 'fa-sun' : 'fa-moon'"></i>
      </button>
    </div>

    <a
      class="logout-btn"
      href="#"
      (click)="logout($event)"
      *ngIf="isAuthenticated()"
    >
      <i class="fas fa-sign-out-alt"></i>
      <span *ngIf="!collapsed || isHovered">Çıkış Yap</span>
    </a>
  </div>
</div>
