using System;
using System.Collections.Generic;

namespace Entities.DTOs
{
    /// <summary>
    /// Cache istatistikleri DTO
    /// </summary>
    public class CacheStatisticsDto
    {
        public int CompanyId { get; set; }
        public int TotalKeys { get; set; }
        public long TotalMemoryUsage { get; set; }
        public double TotalMemoryUsageMB { get; set; }
        public Dictionary<string, int> KeysByEntity { get; set; } = new Dictionary<string, int>();
        public DateTime LastUpdated { get; set; }
    }

    /// <summary>
    /// Cache detayları DTO
    /// </summary>
    public class CacheDetailsDto
    {
        public CacheStatisticsDto Statistics { get; set; }
        public CacheHealthDto Health { get; set; }
        public int CompanyId { get; set; }
        public string[] CachePatterns { get; set; }
    }

    /// <summary>
    /// Cache sağlık durumu DTO
    /// </summary>
    public class CacheHealthDto
    {
        public bool IsConnected { get; set; }
        public double PingTime { get; set; }
        public long ResponseTime { get; set; }
        public string Status { get; set; }
        public CacheServerInfoDto ServerInfo { get; set; }
    }

    /// <summary>
    /// Redis server bilgileri DTO
    /// </summary>
    public class CacheServerInfoDto
    {
        public string Version { get; set; }
        public string UptimeInSeconds { get; set; }
        public string UsedMemory { get; set; }
        public string UsedMemoryHuman { get; set; }
        public string MaxMemory { get; set; }
        public string MaxMemoryHuman { get; set; }
    }

    /// <summary>
    /// Cache key detayları DTO
    /// </summary>
    public class CacheKeyDetailDto
    {
        public string Key { get; set; }
        public string Type { get; set; }
        public double? TTL { get; set; }
        public int MemoryUsage { get; set; }
        public DateTime? CreatedAt { get; set; }
    }

    /// <summary>
    /// Cache keys response DTO
    /// </summary>
    public class CacheKeysResponseDto
    {
        public List<CacheKeyDetailDto> Keys { get; set; } = new List<CacheKeyDetailDto>();
        public CachePaginationDto Pagination { get; set; }
        public string Pattern { get; set; }
    }

    /// <summary>
    /// Cache pagination DTO
    /// </summary>
    public class CachePaginationDto
    {
        public int CurrentPage { get; set; }
        public int PageSize { get; set; }
        public int TotalCount { get; set; }
        public int TotalPages { get; set; }
        public bool HasNextPage { get; set; }
        public bool HasPreviousPage { get; set; }
    }

    /// <summary>
    /// Cache key değeri DTO
    /// </summary>
    public class CacheKeyValueDto
    {
        public string Key { get; set; }
        public string Value { get; set; }
        public bool HasValue { get; set; }
        public double? TTL { get; set; }
        public int Size { get; set; }
    }

    /// <summary>
    /// Cache temizleme sonucu DTO
    /// </summary>
    public class CacheClearResultDto
    {
        public long RemovedCount { get; set; }
        public string Pattern { get; set; }
        public int? CompanyId { get; set; }
        public string CompanyName { get; set; }
    }

    /// <summary>
    /// Cache key silme sonucu DTO
    /// </summary>
    public class CacheKeyDeleteResultDto
    {
        public string Key { get; set; }
        public bool Removed { get; set; }
        public string Message { get; set; }
    }
}
